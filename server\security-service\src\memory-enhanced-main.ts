/**
 * 内存增强安全防护体系服务启动文件
 * 使用内存存储的完整安全服务
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MemoryEnhancedAppModule } from './memory-enhanced-app.module';

async function bootstrap() {
  const logger = new Logger('MemoryEnhancedSecurityService');
  
  try {
    console.log('🚀 开始启动内存增强安全防护体系服务...');
    
    // 创建应用实例
    const app = await NestFactory.create(MemoryEnhancedAppModule, {
      logger: ['log', 'error', 'warn', 'debug'],
    });

    console.log('✅ 应用创建成功');

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    const globalPrefix = 'api/v1';
    app.setGlobalPrefix(globalPrefix);

    // 启用全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: false,
    }));

    // 启用CORS
    app.enableCors({
      origin: '*',
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    console.log('✅ 基础配置完成');

    // 配置Swagger文档
    const swaggerConfig = new DocumentBuilder()
      .setTitle('内存增强安全防护体系服务API')
      .setDescription(`
        DL引擎内存增强安全防护体系服务API文档
        
        🔐 核心功能：
        - 零信任安全架构
        - 威胁检测与响应
        - 安全策略管理
        - 风险评估与监控
        - 身份认证与授权
        - 安全事件管理
        - 合规性检查
        
        💾 内存存储：
        - 高性能内存存储
        - 实时数据处理
        - 快速响应能力
        - 无数据库依赖
        - 轻量级部署
        
        🛡️ 高级特性：
        - 实时威胁监控
        - 智能风险评估
        - 自动化响应机制
        - 合规性报告
        - 性能监控
        - 零延迟数据访问
      `)
      .setVersion('2.1.0')
      .addTag('security', '🔐 安全管理')
      .addTag('zero-trust', '🛡️ 零信任架构')
      .addTag('threat-detection', '🔍 威胁检测')
      .addTag('monitoring', '📊 安全监控')
      .addTag('compliance', '📋 合规检查')
      .addTag('memory-storage', '💾 内存存储')
      .addBearerAuth()
      .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' }, 'api-key')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup(`${globalPrefix}/docs`, app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
        filter: true,
        showRequestDuration: true,
      },
      customSiteTitle: '内存增强安全防护体系服务API文档',
      customfavIcon: '/favicon.ico',
    });

    console.log('✅ Swagger配置完成');

    // 启动HTTP服务
    const port = 11000;
    const host = '127.0.0.1';

    await app.listen(port, host);

    logger.log(`🚀 内存增强安全防护体系服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🌐 全局前缀: /${globalPrefix}`);
    logger.log(`📚 API文档: http://${host}:${port}/${globalPrefix}/docs`);
    logger.log(`📊 健康检查: http://${host}:${port}/${globalPrefix}/health`);
    logger.log(`🔐 安全管理: http://${host}:${port}/${globalPrefix}/security`);
    logger.log(`🛡️ 零信任认证: http://${host}:${port}/${globalPrefix}/security/zero-trust`);
    logger.log(`🔍 威胁检测: http://${host}:${port}/${globalPrefix}/security/threat-detection`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);
    logger.log(`💾 存储模式: 内存存储（高性能模式）`);

    // 输出功能特性
    console.log('\n🎯 核心功能特性:');
    console.log('  ✅ 零信任安全架构');
    console.log('  ✅ 实时威胁检测');
    console.log('  ✅ 安全策略管理');
    console.log('  ✅ 风险评估分析');
    console.log('  ✅ 安全事件监控');
    console.log('  ✅ 设备信任评估');
    console.log('  ✅ 合规性检查');
    console.log('  ✅ 安全指标统计');
    console.log('  ✅ 高性能内存存储');
    console.log('  ✅ 零延迟数据访问');
    console.log('  ✅ 轻量级部署');

    console.log('\n💡 性能优势:');
    console.log('  🚀 毫秒级响应时间');
    console.log('  📈 高并发处理能力');
    console.log('  💾 零数据库依赖');
    console.log('  🔄 实时数据同步');
    console.log('  ⚡ 极速启动时间');

  } catch (error) {
    logger.error('服务启动失败:', error);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise);
  process.exit(1);
});

bootstrap();
