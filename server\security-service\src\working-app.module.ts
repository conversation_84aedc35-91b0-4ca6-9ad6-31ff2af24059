/**
 * 工作版应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

// 控制器
import { SimpleController } from './simple-controller';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // JWT模块
    JwtModule.register({
      secret: 'security-service-jwt-secret-key-change-in-production',
      signOptions: {
        expiresIn: '24h',
      },
    }),
  ],
  controllers: [
    SimpleController,
  ],
  providers: [],
})
export class WorkingAppModule {
  constructor() {
    console.log('🔐 工作版安全防护体系服务模块已加载');
  }
}
