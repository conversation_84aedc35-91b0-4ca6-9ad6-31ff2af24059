"use strict";
/**
 * 工作版应用模块
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkingAppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
// 控制器
const simple_controller_1 = require("./simple-controller");
let WorkingAppModule = class WorkingAppModule {
    constructor() {
        console.log('🔐 工作版安全防护体系服务模块已加载');
    }
};
exports.WorkingAppModule = WorkingAppModule;
exports.WorkingAppModule = WorkingAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            // 配置模块
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
                cache: true,
            }),
            // JWT模块
            jwt_1.JwtModule.register({
                secret: 'security-service-jwt-secret-key-change-in-production',
                signOptions: {
                    expiresIn: '24h',
                },
            }),
        ],
        controllers: [
            simple_controller_1.SimpleController,
        ],
        providers: [],
    }),
    __metadata("design:paramtypes", [])
], WorkingAppModule);
