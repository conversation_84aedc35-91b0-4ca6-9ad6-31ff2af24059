"use strict";
/**
 * 最小化启动文件
 */
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const minimal_app_module_1 = require("./minimal-app.module");
async function bootstrap() {
    const logger = new common_1.Logger('MinimalSecurityService');
    try {
        console.log('🚀 开始启动最小化安全服务...');
        // 创建应用实例
        const app = await core_1.NestFactory.create(minimal_app_module_1.MinimalAppModule, {
            logger: ['log', 'error', 'warn'],
        });
        console.log('✅ 应用创建成功');
        // 启用CORS
        app.enableCors();
        // 启动HTTP服务
        const port = 5557;
        const host = '127.0.0.1';
        await app.listen(port, host);
        logger.log(`🚀 最小化安全防护体系服务已启动`);
        logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
        logger.log(`📊 健康检查: http://${host}:${port}/`);
    }
    catch (error) {
        logger.error('服务启动失败:', error);
        console.error('详细错误:', error);
        process.exit(1);
    }
}
bootstrap();
