/**
 * JWT策略
 */

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

export interface JwtPayload {
  sub: string; // 用户ID
  username: string;
  email: string;
  role: string;
  permissions: string[];
  deviceId?: string;
  sessionId?: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET', 'security-service-jwt-secret-key-change-in-production'),
      passReqToCallback: true,
    });
  }

  async validate(req: any, payload: JwtPayload): Promise<any> {
    try {
      // 基础验证
      if (!payload.sub || !payload.username) {
        throw new UnauthorizedException('无效的令牌载荷');
      }

      // 获取客户端IP
      const clientIp = this.getClientIp(req);

      // 返回用户信息和权限
      return {
        userId: payload.sub,
        username: payload.username,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions || [],
        deviceId: payload.deviceId,
        sessionId: payload.sessionId,
        clientIp,
      };
    } catch (error) {
      throw new UnauthorizedException(error instanceof Error ? error.message : '认证失败');
    }
  }

  private getClientIp(req: any): string {
    return req.headers['x-forwarded-for'] ||
           req.headers['x-real-ip'] ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           '127.0.0.1';
  }
}
