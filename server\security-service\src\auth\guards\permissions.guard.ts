/**
 * 权限守卫
 */

import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../entities/user.entity';

export const PERMISSIONS_KEY = 'permissions';

export interface RequiredPermission {
  resource: string;
  action: string;
}

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<RequiredPermission[]>(PERMISSIONS_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredPermissions) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    
    if (!user) {
      throw new ForbiddenException('用户信息不存在');
    }

    // 管理员拥有所有权限
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // 检查用户是否拥有所需权限
    const hasPermission = requiredPermissions.every(permission => 
      this.checkUserPermission(user, permission.resource, permission.action)
    );

    if (!hasPermission) {
      const permissionStrings = requiredPermissions.map(p => `${p.resource}:${p.action}`);
      throw new ForbiddenException(`需要以下权限: ${permissionStrings.join(', ')}`);
    }

    return true;
  }

  private checkUserPermission(user: any, resource: string, action: string): boolean {
    if (!user.permissions || !Array.isArray(user.permissions)) {
      return false;
    }

    return user.permissions.some((permission: any) => 
      permission.resource === resource && 
      permission.actions.includes(action)
    );
  }
}
