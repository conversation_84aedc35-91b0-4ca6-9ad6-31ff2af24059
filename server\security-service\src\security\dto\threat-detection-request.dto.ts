/**
 * 威胁检测请求DTO
 */

import { IsString, IsOptional, IsEnum, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ScanTypeDto {
  NETWORK = 'network',
  SYSTEM = 'system',
  APPLICATION = 'application',
  DATA = 'data',
  COMPREHENSIVE = 'comprehensive',
}

export class ThreatDetectionRequestDto {
  @ApiProperty({ description: '扫描目标' })
  @IsString()
  target: string;

  @ApiPropertyOptional({
    enum: ScanTypeDto,
    description: '扫描类型',
    default: ScanTypeDto.COMPREHENSIVE,
  })
  @IsOptional()
  @IsEnum(ScanTypeDto)
  scanType?: ScanTypeDto;

  @ApiPropertyOptional({ description: '扫描深度', default: 'medium' })
  @IsOptional()
  @IsString()
  depth?: string;

  @ApiPropertyOptional({ type: [String], description: '扫描范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  scope?: string[];

  @ApiPropertyOptional({ description: '是否包含漏洞扫描', default: true })
  @IsOptional()
  includeVulnerabilities?: boolean;

  @ApiPropertyOptional({ description: '是否包含恶意软件检测', default: true })
  @IsOptional()
  includeMalware?: boolean;
}
