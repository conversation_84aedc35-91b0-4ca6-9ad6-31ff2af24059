"use strict";
/**
 * 权限守卫
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsGuard = exports.PERMISSIONS_KEY = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const user_entity_1 = require("../entities/user.entity");
exports.PERMISSIONS_KEY = 'permissions';
let PermissionsGuard = class PermissionsGuard {
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredPermissions = this.reflector.getAllAndOverride(exports.PERMISSIONS_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredPermissions) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        if (!user) {
            throw new common_1.ForbiddenException('用户信息不存在');
        }
        // 管理员拥有所有权限
        if (user.role === user_entity_1.UserRole.ADMIN) {
            return true;
        }
        // 检查用户是否拥有所需权限
        const hasPermission = requiredPermissions.every(permission => this.checkUserPermission(user, permission.resource, permission.action));
        if (!hasPermission) {
            const permissionStrings = requiredPermissions.map(p => `${p.resource}:${p.action}`);
            throw new common_1.ForbiddenException(`需要以下权限: ${permissionStrings.join(', ')}`);
        }
        return true;
    }
    checkUserPermission(user, resource, action) {
        if (!user.permissions || !Array.isArray(user.permissions)) {
            return false;
        }
        return user.permissions.some((permission) => permission.resource === resource &&
            permission.actions.includes(action));
    }
};
exports.PermissionsGuard = PermissionsGuard;
exports.PermissionsGuard = PermissionsGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], PermissionsGuard);
