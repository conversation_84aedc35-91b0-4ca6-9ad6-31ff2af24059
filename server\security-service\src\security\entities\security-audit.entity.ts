/**
 * 安全审计实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('security_audits')
@Index(['auditType'])
@Index(['status'])
@Index(['initiatedBy'])
@Index(['startTime'])
export class SecurityAudit {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  auditId: string;

  @Column()
  auditType: string;

  @Column()
  scope: string;

  @Column()
  status: string;

  @Column({ nullable: true })
  initiatedBy: string;

  @Column({ type: 'datetime' })
  startTime: Date;

  @Column({ type: 'datetime', nullable: true })
  endTime: Date;

  @Column('json', { nullable: true })
  results: any;

  @Column({ type: 'int', default: 0 })
  riskScore: number;

  @Column({ type: 'int', default: 0 })
  complianceScore: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
