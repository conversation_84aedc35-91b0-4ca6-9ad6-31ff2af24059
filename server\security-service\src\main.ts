/**
 * 安全防护体系服务启动文件
 * 零信任安全架构、区块链数据保护、AI安全防护、隐私计算
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('SecurityService');
  
  try {
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    const globalPrefix = 'api/v1';
    app.setGlobalPrefix(globalPrefix);

    // 启用全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: false,
    }));

    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    });

    // 启用压缩
    app.use(compression());

    // 启用安全头
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // 配置Swagger文档
    const swaggerConfig = new DocumentBuilder()
      .setTitle('安全防护体系服务API')
      .setDescription(`
        DL引擎安全防护体系服务API文档
        
        主要功能：
        - 零信任安全架构
        - 区块链数据保护
        - AI安全防护
        - 隐私计算
        - 身份认证与授权
        - 数据加密与解密
        - 安全事件监控
        - 合规性检查
      `)
      .setVersion('1.0.0')
      .addTag('security', '安全管理')
      .addTag('auth', '身份认证')
      .addTag('encryption', '数据加密')
      .addTag('blockchain', '区块链安全')
      .addTag('privacy', '隐私计算')
      .addTag('monitoring', '安全监控')
      .addTag('compliance', '合规检查')
      .addBearerAuth()
      .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' }, 'api-key')
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup(`${globalPrefix}/docs`, app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
    });

    // 配置微服务
    const microservicePort = configService.get<number>('SECURITY_SERVICE_PORT', 3020);
    const microserviceOptions: MicroserviceOptions = {
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('SECURITY_SERVICE_HOST', '0.0.0.0'),
        port: microservicePort,
      },
    };

    // 连接微服务
    app.connectMicroservice(microserviceOptions);
    await app.startAllMicroservices();
    logger.log(`📡 微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3020);
    const host = configService.get<string>('HOST', '0.0.0.0');

    await app.listen(port, host);

    logger.log(`🚀 安全防护体系服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🌐 全局前缀: /${globalPrefix}`);
    logger.log(`📚 API文档: http://${host}:${port}/${globalPrefix}/docs`);
    logger.log(`📊 健康检查: http://${host}:${port}/${globalPrefix}/health`);
    logger.log(`🔧 环境: ${configService.get<string>('NODE_ENV', 'development')}`);

  } catch (error) {
    logger.error('服务启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在优雅关闭...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在优雅关闭...');
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise);
  process.exit(1);
});

bootstrap();
