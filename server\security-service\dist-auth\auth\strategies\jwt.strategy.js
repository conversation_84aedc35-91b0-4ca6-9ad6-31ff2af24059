"use strict";
/**
 * JWT策略
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    constructor(configService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET', 'security-service-jwt-secret-key-change-in-production'),
            passReqToCallback: true,
        });
        this.configService = configService;
    }
    async validate(req, payload) {
        try {
            // 基础验证
            if (!payload.sub || !payload.username) {
                throw new common_1.UnauthorizedException('无效的令牌载荷');
            }
            // 获取客户端IP
            const clientIp = this.getClientIp(req);
            // 返回用户信息和权限
            return {
                userId: payload.sub,
                username: payload.username,
                email: payload.email,
                role: payload.role,
                permissions: payload.permissions || [],
                deviceId: payload.deviceId,
                sessionId: payload.sessionId,
                clientIp,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error instanceof Error ? error.message : '认证失败');
        }
    }
    getClientIp(req) {
        return req.headers['x-forwarded-for'] ||
            req.headers['x-real-ip'] ||
            req.connection?.remoteAddress ||
            req.socket?.remoteAddress ||
            req.ip ||
            '127.0.0.1';
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], JwtStrategy);
