"use strict";
/**
 * 工作版安全防护体系服务启动文件
 */
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const swagger_1 = require("@nestjs/swagger");
const working_app_module_1 = require("./working-app.module");
async function bootstrap() {
    const logger = new common_1.Logger('WorkingSecurityService');
    try {
        console.log('🚀 开始启动工作版安全防护体系服务...');
        // 创建应用实例
        const app = await core_1.NestFactory.create(working_app_module_1.WorkingAppModule, {
            logger: ['log', 'error', 'warn'],
        });
        console.log('✅ 应用创建成功');
        // 获取配置服务
        const configService = app.get(config_1.ConfigService);
        // 设置全局前缀
        const globalPrefix = 'api/v1';
        app.setGlobalPrefix(globalPrefix);
        // 启用全局验证管道
        app.useGlobalPipes(new common_1.ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
        }));
        // 启用CORS
        app.enableCors({
            origin: '*',
            credentials: true,
        });
        console.log('✅ 基础配置完成');
        // 配置Swagger文档
        const swaggerConfig = new swagger_1.DocumentBuilder()
            .setTitle('安全防护体系服务API')
            .setDescription('DL引擎安全防护体系服务API文档')
            .setVersion('1.0.0')
            .addTag('security', '安全管理')
            .addBearerAuth()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig);
        swagger_1.SwaggerModule.setup(`${globalPrefix}/docs`, app, document);
        console.log('✅ Swagger配置完成');
        // 启动HTTP服务
        const port = 8888;
        const host = '127.0.0.1';
        await app.listen(port, host);
        logger.log(`🚀 工作版安全防护体系服务已启动`);
        logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
        logger.log(`🌐 全局前缀: /${globalPrefix}`);
        logger.log(`📚 API文档: http://${host}:${port}/${globalPrefix}/docs`);
        logger.log(`📊 健康检查: http://${host}:${port}/health`);
    }
    catch (error) {
        logger.error('服务启动失败:', error);
        console.error('详细错误:', error);
        process.exit(1);
    }
}
bootstrap();
