"use strict";
/**
 * 安全事件实体
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityEvent = exports.EventType = exports.EventStatus = exports.EventSeverity = void 0;
const typeorm_1 = require("typeorm");
var EventSeverity;
(function (EventSeverity) {
    EventSeverity["LOW"] = "low";
    EventSeverity["MEDIUM"] = "medium";
    EventSeverity["HIGH"] = "high";
    EventSeverity["CRITICAL"] = "critical";
})(EventSeverity || (exports.EventSeverity = EventSeverity = {}));
var EventStatus;
(function (EventStatus) {
    EventStatus["OPEN"] = "open";
    EventStatus["INVESTIGATING"] = "investigating";
    EventStatus["RESOLVED"] = "resolved";
    EventStatus["FALSE_POSITIVE"] = "false_positive";
    EventStatus["CLOSED"] = "closed";
})(EventStatus || (exports.EventStatus = EventStatus = {}));
var EventType;
(function (EventType) {
    EventType["AUTHENTICATION_FAILURE"] = "authentication_failure";
    EventType["AUTHORIZATION_VIOLATION"] = "authorization_violation";
    EventType["SUSPICIOUS_ACTIVITY"] = "suspicious_activity";
    EventType["MALWARE_DETECTION"] = "malware_detection";
    EventType["INTRUSION_ATTEMPT"] = "intrusion_attempt";
    EventType["DATA_BREACH"] = "data_breach";
    EventType["POLICY_VIOLATION"] = "policy_violation";
    EventType["SYSTEM_COMPROMISE"] = "system_compromise";
    EventType["NETWORK_ANOMALY"] = "network_anomaly";
    EventType["PRIVILEGE_ESCALATION"] = "privilege_escalation";
})(EventType || (exports.EventType = EventType = {}));
let SecurityEvent = class SecurityEvent {
};
exports.SecurityEvent = SecurityEvent;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SecurityEvent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "eventId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EventType,
    }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "eventType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EventSeverity,
        default: EventSeverity.LOW,
    }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EventStatus,
        default: EventStatus.OPEN,
    }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SecurityEvent.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "target", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    __metadata("design:type", String)
], SecurityEvent.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "deviceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityEvent.prototype, "evidence", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityEvent.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Array)
], SecurityEvent.prototype, "riskFactors", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], SecurityEvent.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 100 }),
    __metadata("design:type", Number)
], SecurityEvent.prototype, "trustScore", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityEvent.prototype, "response", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "resolvedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "resolvedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "resolution", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime' }),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "timestamp", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "detectedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "acknowledgedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "escalatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Array)
], SecurityEvent.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], SecurityEvent.prototype, "falsePositive", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], SecurityEvent.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], SecurityEvent.prototype, "updatedAt", void 0);
exports.SecurityEvent = SecurityEvent = __decorate([
    (0, typeorm_1.Entity)('security_events'),
    (0, typeorm_1.Index)(['eventType', 'severity']),
    (0, typeorm_1.Index)(['status']),
    (0, typeorm_1.Index)(['timestamp']),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['ipAddress'])
], SecurityEvent);
