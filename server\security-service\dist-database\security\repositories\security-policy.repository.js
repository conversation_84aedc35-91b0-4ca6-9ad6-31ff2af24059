"use strict";
/**
 * 安全策略仓库
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SecurityPolicyRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityPolicyRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const security_policy_entity_1 = require("../entities/security-policy.entity");
let SecurityPolicyRepository = SecurityPolicyRepository_1 = class SecurityPolicyRepository {
    constructor(repository) {
        this.repository = repository;
        this.logger = new common_1.Logger(SecurityPolicyRepository_1.name);
    }
    /**
     * 创建安全策略
     */
    async create(policyData) {
        try {
            const policy = this.repository.create(policyData);
            const savedPolicy = await this.repository.save(policy);
            this.logger.log(`安全策略已创建: ${savedPolicy.policyId}`);
            return savedPolicy;
        }
        catch (error) {
            this.logger.error('创建安全策略失败', error);
            throw error;
        }
    }
    /**
     * 根据ID查找策略
     */
    async findById(id) {
        try {
            return await this.repository.findOne({ where: { id } });
        }
        catch (error) {
            this.logger.error('查找安全策略失败', error);
            throw error;
        }
    }
    /**
     * 根据策略ID查找策略
     */
    async findByPolicyId(policyId) {
        try {
            return await this.repository.findOne({ where: { policyId } });
        }
        catch (error) {
            this.logger.error('查找安全策略失败', error);
            throw error;
        }
    }
    /**
     * 获取所有活跃策略
     */
    async findActivePolicies() {
        try {
            return await this.repository.find({
                where: {
                    status: security_policy_entity_1.PolicyStatus.ACTIVE,
                    enabled: true,
                },
                order: { priority: 'ASC' },
            });
        }
        catch (error) {
            this.logger.error('获取活跃策略失败', error);
            throw error;
        }
    }
    /**
     * 根据类型获取策略
     */
    async findByType(policyType) {
        try {
            return await this.repository.find({
                where: {
                    policyType,
                    enabled: true,
                },
                order: { priority: 'ASC' },
            });
        }
        catch (error) {
            this.logger.error('根据类型获取策略失败', error);
            throw error;
        }
    }
    /**
     * 更新策略
     */
    async update(id, updateData) {
        try {
            await this.repository.update(id, updateData);
            const updatedPolicy = await this.findById(id);
            if (updatedPolicy) {
                this.logger.log(`安全策略已更新: ${updatedPolicy.policyId}`);
            }
            return updatedPolicy;
        }
        catch (error) {
            this.logger.error('更新安全策略失败', error);
            throw error;
        }
    }
    /**
     * 删除策略
     */
    async delete(id) {
        try {
            const policy = await this.findById(id);
            if (policy) {
                await this.repository.delete(id);
                this.logger.log(`安全策略已删除: ${policy.policyId}`);
            }
        }
        catch (error) {
            this.logger.error('删除安全策略失败', error);
            throw error;
        }
    }
    /**
     * 获取策略统计
     */
    async getStatistics() {
        try {
            const [total, active, inactive] = await Promise.all([
                this.repository.count(),
                this.repository.count({ where: { status: security_policy_entity_1.PolicyStatus.ACTIVE } }),
                this.repository.count({ where: { status: security_policy_entity_1.PolicyStatus.INACTIVE } }),
            ]);
            return {
                total,
                active,
                inactive,
                draft: total - active - inactive,
            };
        }
        catch (error) {
            this.logger.error('获取策略统计失败', error);
            throw error;
        }
    }
    /**
     * 分页查询策略
     */
    async findWithPagination(page = 1, limit = 10, filters) {
        try {
            const skip = (page - 1) * limit;
            const where = {};
            // 应用过滤器
            if (filters?.status) {
                where.status = filters.status;
            }
            if (filters?.policyType) {
                where.policyType = filters.policyType;
            }
            if (filters?.enabled !== undefined) {
                where.enabled = filters.enabled;
            }
            const [policies, total] = await this.repository.findAndCount({
                where,
                skip,
                take: limit,
                order: { priority: 'ASC', createdAt: 'DESC' },
            });
            return {
                policies,
                total,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('分页查询策略失败', error);
            throw error;
        }
    }
    /**
     * 批量更新策略状态
     */
    async batchUpdateStatus(ids, status) {
        try {
            await this.repository.update(ids, { status });
            this.logger.log(`批量更新策略状态: ${ids.length} 个策略更新为 ${status}`);
        }
        catch (error) {
            this.logger.error('批量更新策略状态失败', error);
            throw error;
        }
    }
    /**
     * 搜索策略
     */
    async search(keyword) {
        try {
            return await this.repository
                .createQueryBuilder('policy')
                .where('policy.name LIKE :keyword', { keyword: `%${keyword}%` })
                .orWhere('policy.description LIKE :keyword', { keyword: `%${keyword}%` })
                .orWhere('policy.policyId LIKE :keyword', { keyword: `%${keyword}%` })
                .orderBy('policy.priority', 'ASC')
                .getMany();
        }
        catch (error) {
            this.logger.error('搜索策略失败', error);
            throw error;
        }
    }
};
exports.SecurityPolicyRepository = SecurityPolicyRepository;
exports.SecurityPolicyRepository = SecurityPolicyRepository = SecurityPolicyRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(security_policy_entity_1.SecurityPolicy)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SecurityPolicyRepository);
