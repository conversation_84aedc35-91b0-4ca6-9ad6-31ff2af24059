-- 安全防护体系服务数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS security_service 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE security_service;

-- 创建用户和权限
CREATE USER IF NOT EXISTS 'security_user'@'%' IDENTIFIED BY 'security_password';
GRANT ALL PRIVILEGES ON security_service.* TO 'security_user'@'%';
FLUSH PRIVILEGES;

-- 安全策略表
CREATE TABLE IF NOT EXISTS security_policies (
    id VARCHAR(36) PRIMARY KEY,
    policyId VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    policyType ENUM('access_control', 'data_protection', 'network_security', 'device_compliance', 'zero_trust') DEFAULT 'access_control',
    enforcementMode ENUM('monitor', 'enforce', 'block') DEFAULT 'monitor',
    status ENUM('active', 'inactive', 'draft', 'archived') DEFAULT 'draft',
    priority INT DEFAULT 5,
    scope JSON,
    rules JSO<PERSON>,
    conditions JSO<PERSON>,
    actions JSO<PERSON>,
    createdBy VARCHAR(255),
    updatedBy VARCHAR(255),
    effectiveFrom DATETIME,
    effectiveTo DATETIME,
    violationCount INT DEFAULT 0,
    lastViolation DATETIME,
    enabled BOOLEAN DEFAULT TRUE,
    metadata JSON,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_policy_type_status (policyType, status),
    INDEX idx_created_by (createdBy),
    INDEX idx_priority (priority)
);

-- 安全事件表
CREATE TABLE IF NOT EXISTS security_events (
    id VARCHAR(36) PRIMARY KEY,
    eventId VARCHAR(255) UNIQUE NOT NULL,
    eventType ENUM('authentication_failure', 'authorization_violation', 'suspicious_activity', 'malware_detection', 'intrusion_attempt', 'data_breach', 'policy_violation', 'system_compromise', 'network_anomaly', 'privilege_escalation') NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
    status ENUM('open', 'investigating', 'resolved', 'false_positive', 'closed') DEFAULT 'open',
    source VARCHAR(255) NOT NULL,
    target VARCHAR(255),
    description TEXT NOT NULL,
    userId VARCHAR(255),
    sessionId VARCHAR(255),
    deviceId VARCHAR(255),
    ipAddress VARCHAR(45),
    userAgent TEXT,
    location VARCHAR(255),
    evidence JSON,
    metadata JSON,
    riskFactors JSON,
    riskScore INT DEFAULT 0,
    trustScore INT DEFAULT 100,
    response JSON,
    assignedTo VARCHAR(255),
    resolvedBy VARCHAR(255),
    resolvedAt DATETIME,
    resolution TEXT,
    timestamp DATETIME NOT NULL,
    detectedAt DATETIME,
    acknowledgedAt DATETIME,
    escalatedAt DATETIME,
    tags JSON,
    falsePositive BOOLEAN DEFAULT FALSE,
    notes TEXT,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_event_type_severity (eventType, severity),
    INDEX idx_status (status),
    INDEX idx_timestamp (timestamp),
    INDEX idx_user_id (userId),
    INDEX idx_ip_address (ipAddress)
);

-- 威胁情报表
CREATE TABLE IF NOT EXISTS threat_intelligence (
    id VARCHAR(36) PRIMARY KEY,
    threatId VARCHAR(255) UNIQUE NOT NULL,
    threatType VARCHAR(255) NOT NULL,
    severity VARCHAR(50) NOT NULL,
    source VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    indicators JSON,
    metadata JSON,
    active BOOLEAN DEFAULT TRUE,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_threat_type_severity (threatType, severity),
    INDEX idx_source (source),
    INDEX idx_created_at (createdAt)
);

-- 安全审计表
CREATE TABLE IF NOT EXISTS security_audits (
    id VARCHAR(36) PRIMARY KEY,
    auditId VARCHAR(255) UNIQUE NOT NULL,
    auditType VARCHAR(255) NOT NULL,
    scope VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    initiatedBy VARCHAR(255),
    startTime DATETIME NOT NULL,
    endTime DATETIME,
    results JSON,
    riskScore INT DEFAULT 0,
    complianceScore INT DEFAULT 0,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_audit_type (auditType),
    INDEX idx_status (status),
    INDEX idx_initiated_by (initiatedBy),
    INDEX idx_start_time (startTime)
);

-- 设备信任表
CREATE TABLE IF NOT EXISTS device_trust (
    id VARCHAR(36) PRIMARY KEY,
    deviceId VARCHAR(255) UNIQUE NOT NULL,
    trustScore INT DEFAULT 0,
    complianceStatus VARCHAR(50) DEFAULT 'unknown',
    securityPosture JSON,
    lastAssessment DATETIME,
    certificates JSON,
    vulnerabilities JSON,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_id (deviceId),
    INDEX idx_trust_score (trustScore),
    INDEX idx_compliance_status (complianceStatus)
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    sessionId VARCHAR(255) UNIQUE NOT NULL,
    userId VARCHAR(255) NOT NULL,
    deviceId VARCHAR(255),
    ipAddress VARCHAR(45),
    userAgent TEXT,
    status VARCHAR(50) DEFAULT 'active',
    trustScore INT DEFAULT 100,
    riskScore INT DEFAULT 0,
    metadata JSON,
    lastActivity DATETIME,
    expiresAt DATETIME,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (userId),
    INDEX idx_session_id (sessionId),
    INDEX idx_status (status),
    INDEX idx_created_at (createdAt)
);

-- 安全指标表
CREATE TABLE IF NOT EXISTS security_metrics (
    id VARCHAR(36) PRIMARY KEY,
    metricType VARCHAR(50) DEFAULT 'general',
    totalEvents INT DEFAULT 0,
    criticalEvents INT DEFAULT 0,
    highEvents INT DEFAULT 0,
    mediumEvents INT DEFAULT 0,
    lowEvents INT DEFAULT 0,
    resolvedEvents INT DEFAULT 0,
    activeThreats INT DEFAULT 0,
    blockedAttacks INT DEFAULT 0,
    successfulAuthentications INT DEFAULT 0,
    failedAuthentications INT DEFAULT 0,
    activeSessions INT DEFAULT 0,
    trustedDevices INT DEFAULT 0,
    complianceScore INT DEFAULT 100,
    riskScore INT DEFAULT 0,
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_metric_type (metricType),
    INDEX idx_created_at (createdAt)
);

-- 插入初始数据
INSERT INTO security_policies (id, policyId, name, description, policyType, enforcementMode, status, priority, enabled) VALUES
(UUID(), 'default_access_control', '默认访问控制策略', '系统默认的访问控制策略', 'access_control', 'enforce', 'active', 1, TRUE),
(UUID(), 'zero_trust_default', '零信任默认策略', '零信任架构的默认安全策略', 'zero_trust', 'monitor', 'active', 2, TRUE);

INSERT INTO security_metrics (id, metricType) VALUES
(UUID(), 'general');

-- 创建视图
CREATE VIEW security_dashboard AS
SELECT 
    (SELECT COUNT(*) FROM security_events WHERE status = 'open') as open_events,
    (SELECT COUNT(*) FROM security_events WHERE severity = 'critical' AND status = 'open') as critical_events,
    (SELECT COUNT(*) FROM security_policies WHERE enabled = TRUE) as active_policies,
    (SELECT COUNT(*) FROM user_sessions WHERE status = 'active') as active_sessions,
    (SELECT AVG(trustScore) FROM device_trust) as avg_device_trust,
    (SELECT COUNT(*) FROM threat_intelligence WHERE active = TRUE) as active_threats;

COMMIT;
