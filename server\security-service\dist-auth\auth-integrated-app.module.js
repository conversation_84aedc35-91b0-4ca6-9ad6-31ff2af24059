"use strict";
/**
 * 认证集成应用模块
 * 集成身份认证与授权系统的完整安全服务
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthIntegratedAppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const terminus_1 = require("@nestjs/terminus");
// 认证模块
const auth_module_1 = require("./auth/auth.module");
// 控制器
const simple_controller_1 = require("./simple-controller");
const enhanced_security_controller_1 = require("./enhanced-security.controller");
// 服务
const enhanced_security_service_1 = require("./enhanced-security.service");
// 健康检查
const memory_health_controller_1 = require("./memory-health.controller");
// 内存存储服务
const memory_storage_service_1 = require("./memory-storage.service");
let AuthIntegratedAppModule = class AuthIntegratedAppModule {
    constructor() {
        console.log('🔐 认证集成安全防护体系服务模块已加载');
        console.log('✅ 身份认证与授权系统已集成');
        console.log('✅ JWT令牌认证已启用');
        console.log('✅ 角色权限控制已配置');
        console.log('✅ 用户管理功能已启用');
        console.log('✅ 会话管理已集成');
        console.log('✅ 安全审计已启用');
        console.log('✅ 多因素认证支持已配置');
    }
};
exports.AuthIntegratedAppModule = AuthIntegratedAppModule;
exports.AuthIntegratedAppModule = AuthIntegratedAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            // 配置模块
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
                cache: true,
            }),
            // 认证模块
            auth_module_1.AuthModule,
            // JWT模块（全局配置）
            jwt_1.JwtModule.register({
                secret: 'security-service-jwt-secret-key-change-in-production',
                signOptions: {
                    expiresIn: '1h',
                },
            }),
            // Passport模块
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            // 健康检查模块
            terminus_1.TerminusModule,
        ],
        controllers: [
            simple_controller_1.SimpleController,
            enhanced_security_controller_1.EnhancedSecurityController,
            memory_health_controller_1.MemoryHealthController,
        ],
        providers: [
            // 内存存储服务
            memory_storage_service_1.MemoryStorageService,
            // 业务服务
            enhanced_security_service_1.EnhancedSecurityService,
        ],
        exports: [
            auth_module_1.AuthModule,
            memory_storage_service_1.MemoryStorageService,
            enhanced_security_service_1.EnhancedSecurityService,
        ],
    }),
    __metadata("design:paramtypes", [])
], AuthIntegratedAppModule);
