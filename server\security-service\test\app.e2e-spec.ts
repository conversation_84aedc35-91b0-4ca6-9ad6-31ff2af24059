/**
 * 安全防护体系服务端到端测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('SecurityService (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('应用基础功能', () => {
    it('/ (GET) - 获取服务信息', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('name');
          expect(res.body).toHaveProperty('version');
          expect(res.body).toHaveProperty('description');
          expect(res.body).toHaveProperty('status', 'running');
          expect(res.body).toHaveProperty('features');
          expect(Array.isArray(res.body.features)).toBe(true);
        });
    });

    it('/version (GET) - 获取服务版本', () => {
      return request(app.getHttpServer())
        .get('/version')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('version');
          expect(res.body).toHaveProperty('buildTime');
          expect(res.body).toHaveProperty('environment');
        });
    });

    it('/status (GET) - 获取服务状态', () => {
      return request(app.getHttpServer())
        .get('/status')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('uptime');
          expect(res.body).toHaveProperty('memory');
          expect(res.body).toHaveProperty('cpu');
          expect(res.body).toHaveProperty('security');
        });
    });
  });

  describe('健康检查', () => {
    it('/api/v1/health (GET) - 健康检查', () => {
      return request(app.getHttpServer())
        .get('/api/v1/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('info');
          expect(res.body).toHaveProperty('details');
        });
    });
  });

  describe('安全功能', () => {
    it('/api/v1/security/status (GET) - 获取安全状态', () => {
      return request(app.getHttpServer())
        .get('/api/v1/security/status')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
          expect(res.body).toHaveProperty('data');
          expect(res.body.data).toHaveProperty('overview');
          expect(res.body.data).toHaveProperty('threatLevel');
        });
    });

    it('/api/v1/security/zero-trust/policies (GET) - 获取零信任策略', () => {
      return request(app.getHttpServer())
        .get('/api/v1/security/zero-trust/policies')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
          expect(res.body).toHaveProperty('data');
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });

    it('/api/v1/security/threat-detection/events (GET) - 获取安全事件', () => {
      return request(app.getHttpServer())
        .get('/api/v1/security/threat-detection/events')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('success', true);
          expect(res.body).toHaveProperty('data');
        });
    });
  });

  describe('错误处理', () => {
    it('不存在的路由应返回404', () => {
      return request(app.getHttpServer())
        .get('/api/v1/nonexistent')
        .expect(404);
    });

    it('无效的请求参数应返回400', () => {
      return request(app.getHttpServer())
        .post('/api/v1/security/zero-trust/policies')
        .send({
          // 缺少必需字段
        })
        .expect(400);
    });
  });

  describe('API文档', () => {
    it('/api/v1/docs (GET) - Swagger文档可访问', () => {
      return request(app.getHttpServer())
        .get('/api/v1/docs')
        .expect(200);
    });

    it('/api/v1/docs-json (GET) - API JSON可访问', () => {
      return request(app.getHttpServer())
        .get('/api/v1/docs-json')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('openapi');
          expect(res.body).toHaveProperty('info');
          expect(res.body).toHaveProperty('paths');
        });
    });
  });
});
