/**
 * 安全模块
 * 零信任安全架构核心模块
 */

import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';

// 控制器
import { SecurityController } from './security.controller';

// 服务
import { SecurityService } from './security.service';
import { ZeroTrustSecurityService } from './zero-trust-security.service';
import { ThreatDetectionService } from './threat-detection.service';
import { SecurityPolicyService } from './security-policy.service';
import { RiskAssessmentService } from './risk-assessment.service';

// 实体（暂时注释掉，因为没有数据库连接）
// import { SecurityPolicy } from './entities/security-policy.entity';
// import { SecurityEvent } from './entities/security-event.entity';
// import { ThreatIntelligence } from './entities/threat-intelligence.entity';
// import { SecurityAudit } from './entities/security-audit.entity';
// import { DeviceTrust } from './entities/device-trust.entity';
// import { UserSession } from './entities/user-session.entity';
// import { SecurityMetrics } from './entities/security-metrics.entity';

// 守卫
import { SecurityGuard } from './guards/security.guard';
import { ThreatDetectionGuard } from './guards/threat-detection.guard';
import { RateLimitGuard } from './guards/rate-limit.guard';

// 策略
import { SecurityStrategy } from './strategies/security.strategy';
import { ZeroTrustStrategy } from './strategies/zero-trust.strategy';

// 中间件
import { SecurityMiddleware } from './middleware/security.middleware';
import { AuditMiddleware } from './middleware/audit.middleware';

@Module({
  imports: [
    ConfigModule,
    JwtModule,
    // 暂时移除数据库依赖
    // TypeOrmModule.forFeature([...])
  ],
  controllers: [
    SecurityController,
  ],
  providers: [
    // 核心服务
    SecurityService,
    ZeroTrustSecurityService,
    ThreatDetectionService,
    SecurityPolicyService,
    RiskAssessmentService,

    // 守卫
    SecurityGuard,
    ThreatDetectionGuard,
    RateLimitGuard,

    // 策略
    SecurityStrategy,
    ZeroTrustStrategy,

    // 中间件
    SecurityMiddleware,
    AuditMiddleware,
  ],
  exports: [
    SecurityService,
    ZeroTrustSecurityService,
    ThreatDetectionService,
    SecurityPolicyService,
    RiskAssessmentService,
    SecurityGuard,
    ThreatDetectionGuard,
    RateLimitGuard,
  ],
})
export class SecurityModule {
  constructor() {
    console.log('🔐 安全模块已加载');
  }
}
