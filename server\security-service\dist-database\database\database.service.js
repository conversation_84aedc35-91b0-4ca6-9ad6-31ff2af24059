"use strict";
/**
 * 数据库服务
 * 提供数据库操作的统一接口
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let DatabaseService = DatabaseService_1 = class DatabaseService {
    constructor(configService, dataSource) {
        this.configService = configService;
        this.dataSource = dataSource;
        this.logger = new common_1.Logger(DatabaseService_1.name);
        this.dbEnabled = this.configService.get('DB_ENABLED', 'true') === 'true';
    }
    async onModuleInit() {
        if (this.dbEnabled) {
            await this.initializeDatabase();
        }
        else {
            this.logger.log('数据库已禁用，使用内存存储模式');
        }
    }
    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        try {
            this.logger.log('开始初始化数据库...');
            // 检查数据库连接
            if (this.dataSource && this.dataSource.isInitialized) {
                this.logger.log('数据库连接已建立');
                // 运行数据库迁移（如果需要）
                await this.runMigrations();
                // 初始化基础数据
                await this.seedInitialData();
                this.logger.log('数据库初始化完成');
            }
            else {
                this.logger.warn('数据库连接未建立');
            }
        }
        catch (error) {
            this.logger.error('数据库初始化失败:', error);
            throw error;
        }
    }
    /**
     * 运行数据库迁移
     */
    async runMigrations() {
        try {
            if (this.configService.get('DB_RUN_MIGRATIONS', false)) {
                this.logger.log('运行数据库迁移...');
                await this.dataSource.runMigrations();
                this.logger.log('数据库迁移完成');
            }
        }
        catch (error) {
            this.logger.error('数据库迁移失败:', error);
            throw error;
        }
    }
    /**
     * 初始化基础数据
     */
    async seedInitialData() {
        try {
            this.logger.log('初始化基础数据...');
            // 检查是否已有数据
            const policyCount = await this.dataSource
                .getRepository('SecurityPolicy')
                .count();
            if (policyCount === 0) {
                this.logger.log('插入默认安全策略...');
                await this.insertDefaultPolicies();
            }
            // 检查安全指标表
            const metricsCount = await this.dataSource
                .getRepository('SecurityMetrics')
                .count();
            if (metricsCount === 0) {
                this.logger.log('初始化安全指标...');
                await this.insertInitialMetrics();
            }
            this.logger.log('基础数据初始化完成');
        }
        catch (error) {
            this.logger.error('基础数据初始化失败:', error);
            // 不抛出错误，允许服务继续运行
        }
    }
    /**
     * 插入默认安全策略
     */
    async insertDefaultPolicies() {
        const policyRepository = this.dataSource.getRepository('SecurityPolicy');
        const defaultPolicies = [
            {
                policyId: 'default_access_control',
                name: '默认访问控制策略',
                description: '系统默认的访问控制策略',
                policyType: 'access_control',
                enforcementMode: 'enforce',
                status: 'active',
                priority: 1,
                scope: {
                    users: ['*'],
                    devices: ['*'],
                    applications: ['*'],
                    networks: ['*'],
                    resources: ['*'],
                },
                rules: [
                    {
                        ruleId: 'rule_001',
                        type: 'trust_score',
                        condition: { trustScore: { gte: 70 } },
                        action: { type: 'allow' },
                        weight: 10,
                    }
                ],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                policyId: 'zero_trust_default',
                name: '零信任默认策略',
                description: '零信任架构的默认安全策略',
                policyType: 'zero_trust',
                enforcementMode: 'monitor',
                status: 'active',
                priority: 2,
                scope: {
                    users: ['*'],
                    devices: ['*'],
                    applications: ['*'],
                    networks: ['*'],
                    resources: ['*'],
                },
                rules: [
                    {
                        ruleId: 'rule_002',
                        type: 'device_compliance',
                        condition: { deviceCompliant: true },
                        action: { type: 'allow' },
                        weight: 8,
                    }
                ],
                enabled: true,
                createdAt: new Date(),
                updatedAt: new Date(),
            }
        ];
        for (const policy of defaultPolicies) {
            await policyRepository.save(policy);
        }
        this.logger.log(`已插入 ${defaultPolicies.length} 个默认安全策略`);
    }
    /**
     * 插入初始安全指标
     */
    async insertInitialMetrics() {
        const metricsRepository = this.dataSource.getRepository('SecurityMetrics');
        const initialMetrics = {
            metricType: 'general',
            totalEvents: 0,
            criticalEvents: 0,
            highEvents: 0,
            mediumEvents: 0,
            lowEvents: 0,
            resolvedEvents: 0,
            activeThreats: 0,
            blockedAttacks: 0,
            successfulAuthentications: 0,
            failedAuthentications: 0,
            activeSessions: 0,
            trustedDevices: 0,
            complianceScore: 100,
            riskScore: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        await metricsRepository.save(initialMetrics);
        this.logger.log('已插入初始安全指标');
    }
    /**
     * 获取数据库健康状态
     */
    async getHealthStatus() {
        if (!this.dbEnabled) {
            return {
                status: 'disabled',
                message: '数据库已禁用，使用内存存储',
            };
        }
        try {
            if (this.dataSource && this.dataSource.isInitialized) {
                // 执行简单查询测试连接
                await this.dataSource.query('SELECT 1');
                return {
                    status: 'healthy',
                    message: '数据库连接正常',
                    database: this.configService.get('DB_DATABASE'),
                    host: this.configService.get('DB_HOST'),
                    port: this.configService.get('DB_PORT'),
                };
            }
            else {
                return {
                    status: 'disconnected',
                    message: '数据库连接未建立',
                };
            }
        }
        catch (error) {
            return {
                status: 'error',
                message: '数据库连接异常',
                error: error.message,
            };
        }
    }
    /**
     * 获取数据库统计信息
     */
    async getDatabaseStats() {
        if (!this.dbEnabled || !this.dataSource?.isInitialized) {
            return {
                enabled: false,
                message: '数据库未启用或未连接',
            };
        }
        try {
            const stats = {
                enabled: true,
                tables: {},
                totalRecords: 0,
            };
            // 获取各表记录数
            const entities = [
                'SecurityPolicy',
                'SecurityEvent',
                'ThreatIntelligence',
                'SecurityAudit',
                'DeviceTrust',
                'UserSession',
                'SecurityMetrics',
            ];
            for (const entity of entities) {
                try {
                    const count = await this.dataSource.getRepository(entity).count();
                    stats.tables[entity] = count;
                    stats.totalRecords += count;
                }
                catch (error) {
                    stats.tables[entity] = 'error';
                }
            }
            return stats;
        }
        catch (error) {
            return {
                enabled: true,
                error: error.message,
            };
        }
    }
    /**
     * 检查数据库是否启用
     */
    isDatabaseEnabled() {
        return this.dbEnabled;
    }
    /**
     * 获取数据源
     */
    getDataSource() {
        return this.dbEnabled ? this.dataSource : null;
    }
};
exports.DatabaseService = DatabaseService;
exports.DatabaseService = DatabaseService = DatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [config_1.ConfigService,
        typeorm_2.DataSource])
], DatabaseService);
