/**
 * 内存版健康检查控制器
 */

import { Controller, Get } from '@nestjs/common';
import {
  HealthCheckService,
  HealthCheck,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { MemoryStorageService } from './memory-storage.service';

@ApiTags('health')
@Controller('health')
export class MemoryHealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private memoryStorage: MemoryStorageService,
  ) {}

  @Get()
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ status: 200, description: '健康状态' })
  @HealthCheck()
  check() {
    return this.health.check([
      // 内存存储健康检查
      async () => {
        const storageHealth = await this.memoryStorage.getHealthStatus();
        return {
          'memory-storage': {
            status: storageHealth.status === 'healthy' ? 'up' : 'down',
            ...storageHealth,
          },
        };
      },
      
      // 内存使用检查
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      
      // 磁盘使用检查
      () => this.disk.checkStorage('storage', {
        path: '/',
        thresholdPercent: 0.9,
      }),
    ]);
  }

  @Get('ready')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '就绪状态' })
  @HealthCheck()
  ready() {
    return this.health.check([
      async () => {
        const storageHealth = await this.memoryStorage.getHealthStatus();
        return {
          'memory-storage': {
            status: storageHealth.status === 'healthy' ? 'up' : 'down',
            ...storageHealth,
          },
        };
      },
    ]);
  }

  @Get('live')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '存活状态' })
  @HealthCheck()
  live() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),
    ]);
  }

  @Get('storage')
  @ApiOperation({ summary: '存储状态检查' })
  @ApiResponse({ status: 200, description: '存储状态' })
  async getStorageStatus() {
    try {
      const [healthStatus, statistics] = await Promise.all([
        this.memoryStorage.getHealthStatus(),
        this.memoryStorage.getStorageStatistics(),
      ]);

      return {
        success: true,
        data: {
          health: healthStatus,
          statistics,
          performance: {
            responseTime: '< 1ms',
            throughput: 'unlimited',
            availability: '100%',
          },
        },
        message: '存储状态检查完成',
      };
    } catch (error) {
      return {
        success: false,
        message: '存储状态检查失败',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
