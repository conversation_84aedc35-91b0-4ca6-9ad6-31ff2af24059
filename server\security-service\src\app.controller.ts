/**
 * 安全防护体系服务应用控制器
 */

import { Controller, Get, Post, Body, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AppService } from './app.service';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { Public } from './common/decorators/public.decorator';

@ApiTags('app')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  @Get()
  @Public()
  @ApiOperation({ summary: '获取服务信息' })
  @ApiResponse({ 
    status: 200, 
    description: '服务信息',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: '安全防护体系服务' },
        version: { type: 'string', example: '1.0.0' },
        description: { type: 'string', example: '零信任安全架构、区块链数据保护、AI安全防护、隐私计算' },
        status: { type: 'string', example: 'running' },
        timestamp: { type: 'string', format: 'date-time' },
        features: {
          type: 'array',
          items: { type: 'string' },
          example: ['零信任安全', '区块链保护', 'AI安全防护', '隐私计算']
        }
      }
    }
  })
  getServiceInfo() {
    this.logger.log('获取服务信息');
    return this.appService.getServiceInfo();
  }

  @Get('version')
  @Public()
  @ApiOperation({ summary: '获取服务版本' })
  @ApiResponse({ 
    status: 200, 
    description: '服务版本信息',
    schema: {
      type: 'object',
      properties: {
        version: { type: 'string', example: '1.0.0' },
        buildTime: { type: 'string', format: 'date-time' },
        gitCommit: { type: 'string', example: 'abc123' },
        environment: { type: 'string', example: 'production' }
      }
    }
  })
  getVersion() {
    this.logger.log('获取服务版本');
    return this.appService.getVersion();
  }

  @Get('status')
  @Public()
  @ApiOperation({ summary: '获取服务状态' })
  @ApiResponse({ 
    status: 200, 
    description: '服务状态信息',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        uptime: { type: 'number', example: 3600 },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'number' },
            total: { type: 'number' },
            percentage: { type: 'number' }
          }
        },
        cpu: {
          type: 'object',
          properties: {
            usage: { type: 'number' },
            loadAverage: { type: 'array', items: { type: 'number' } }
          }
        },
        security: {
          type: 'object',
          properties: {
            activePolicies: { type: 'number' },
            activeSessions: { type: 'number' },
            threatLevel: { type: 'string' },
            lastSecurityCheck: { type: 'string', format: 'date-time' }
          }
        }
      }
    }
  })
  async getStatus() {
    this.logger.log('获取服务状态');
    return await this.appService.getStatus();
  }

  @Get('metrics')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取服务指标' })
  @ApiResponse({ 
    status: 200, 
    description: '服务指标信息',
    schema: {
      type: 'object',
      properties: {
        requests: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            success: { type: 'number' },
            error: { type: 'number' },
            rate: { type: 'number' }
          }
        },
        security: {
          type: 'object',
          properties: {
            authenticationAttempts: { type: 'number' },
            successfulAuthentications: { type: 'number' },
            failedAuthentications: { type: 'number' },
            blockedRequests: { type: 'number' },
            securityEvents: { type: 'number' }
          }
        },
        performance: {
          type: 'object',
          properties: {
            averageResponseTime: { type: 'number' },
            p95ResponseTime: { type: 'number' },
            p99ResponseTime: { type: 'number' }
          }
        }
      }
    }
  })
  async getMetrics() {
    this.logger.log('获取服务指标');
    return await this.appService.getMetrics();
  }

  @Post('security-check')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '执行安全检查' })
  @ApiResponse({ 
    status: 200, 
    description: '安全检查结果',
    schema: {
      type: 'object',
      properties: {
        checkId: { type: 'string' },
        timestamp: { type: 'string', format: 'date-time' },
        status: { type: 'string', example: 'completed' },
        results: {
          type: 'object',
          properties: {
            overallScore: { type: 'number' },
            threatLevel: { type: 'string' },
            vulnerabilities: { type: 'array', items: { type: 'object' } },
            recommendations: { type: 'array', items: { type: 'string' } }
          }
        }
      }
    }
  })
  async performSecurityCheck(@Body() checkRequest: any) {
    this.logger.log('执行安全检查', checkRequest);
    return await this.appService.performSecurityCheck(checkRequest);
  }
}
