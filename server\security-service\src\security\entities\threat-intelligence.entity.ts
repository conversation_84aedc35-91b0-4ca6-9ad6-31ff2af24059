/**
 * 威胁情报实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('threat_intelligence')
@Index(['threatType', 'severity'])
@Index(['source'])
@Index(['createdAt'])
export class ThreatIntelligence {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  threatId: string;

  @Column()
  threatType: string;

  @Column()
  severity: string;

  @Column()
  source: string;

  @Column('text')
  description: string;

  @Column('json', { nullable: true })
  indicators: any;

  @Column('json', { nullable: true })
  metadata: any;

  @Column({ type: 'boolean', default: true })
  active: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
