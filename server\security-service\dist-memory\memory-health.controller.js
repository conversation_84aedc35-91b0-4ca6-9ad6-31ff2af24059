"use strict";
/**
 * 内存版健康检查控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryHealthController = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const swagger_1 = require("@nestjs/swagger");
const memory_storage_service_1 = require("./memory-storage.service");
let MemoryHealthController = class MemoryHealthController {
    constructor(health, memory, disk, memoryStorage) {
        this.health = health;
        this.memory = memory;
        this.disk = disk;
        this.memoryStorage = memoryStorage;
    }
    check() {
        return this.health.check([
            // 内存存储健康检查
            async () => {
                const storageHealth = await this.memoryStorage.getHealthStatus();
                return {
                    'memory-storage': {
                        status: storageHealth.status === 'healthy' ? 'up' : 'down',
                        ...storageHealth,
                    },
                };
            },
            // 内存使用检查
            () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
            () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
            // 磁盘使用检查
            () => this.disk.checkStorage('storage', {
                path: '/',
                thresholdPercent: 0.9,
            }),
        ]);
    }
    ready() {
        return this.health.check([
            async () => {
                const storageHealth = await this.memoryStorage.getHealthStatus();
                return {
                    'memory-storage': {
                        status: storageHealth.status === 'healthy' ? 'up' : 'down',
                        ...storageHealth,
                    },
                };
            },
        ]);
    }
    live() {
        return this.health.check([
            () => this.memory.checkHeap('memory_heap', 300 * 1024 * 1024),
        ]);
    }
    async getStorageStatus() {
        try {
            const [healthStatus, statistics] = await Promise.all([
                this.memoryStorage.getHealthStatus(),
                this.memoryStorage.getStorageStatistics(),
            ]);
            return {
                success: true,
                data: {
                    health: healthStatus,
                    statistics,
                    performance: {
                        responseTime: '< 1ms',
                        throughput: 'unlimited',
                        availability: '100%',
                    },
                },
                message: '存储状态检查完成',
            };
        }
        catch (error) {
            return {
                success: false,
                message: '存储状态检查失败',
                error: error.message,
            };
        }
    }
};
exports.MemoryHealthController = MemoryHealthController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '健康检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '健康状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MemoryHealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('ready'),
    (0, swagger_1.ApiOperation)({ summary: '就绪检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '就绪状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MemoryHealthController.prototype, "ready", null);
__decorate([
    (0, common_1.Get)('live'),
    (0, swagger_1.ApiOperation)({ summary: '存活检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '存活状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MemoryHealthController.prototype, "live", null);
__decorate([
    (0, common_1.Get)('storage'),
    (0, swagger_1.ApiOperation)({ summary: '存储状态检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '存储状态' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MemoryHealthController.prototype, "getStorageStatus", null);
exports.MemoryHealthController = MemoryHealthController = __decorate([
    (0, swagger_1.ApiTags)('health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        terminus_1.MemoryHealthIndicator,
        terminus_1.DiskHealthIndicator,
        memory_storage_service_1.MemoryStorageService])
], MemoryHealthController);
