"use strict";
/**
 * 安全事件仓库
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SecurityEventRepository_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityEventRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const security_event_entity_1 = require("../entities/security-event.entity");
let SecurityEventRepository = SecurityEventRepository_1 = class SecurityEventRepository {
    constructor(repository) {
        this.repository = repository;
        this.logger = new common_1.Logger(SecurityEventRepository_1.name);
    }
    /**
     * 创建安全事件
     */
    async create(eventData) {
        try {
            const event = this.repository.create(eventData);
            const savedEvent = await this.repository.save(event);
            this.logger.log(`安全事件已记录: ${savedEvent.eventType} - ${savedEvent.severity}`);
            return savedEvent;
        }
        catch (error) {
            this.logger.error('创建安全事件失败', error);
            throw error;
        }
    }
    /**
     * 根据ID查找事件
     */
    async findById(id) {
        try {
            return await this.repository.findOne({ where: { id } });
        }
        catch (error) {
            this.logger.error('查找安全事件失败', error);
            throw error;
        }
    }
    /**
     * 获取最近的安全事件
     */
    async findRecent(limit = 50) {
        try {
            return await this.repository.find({
                order: { timestamp: 'DESC' },
                take: limit,
            });
        }
        catch (error) {
            this.logger.error('获取最近安全事件失败', error);
            throw error;
        }
    }
    /**
     * 根据严重程度获取事件
     */
    async findBySeverity(severity) {
        try {
            return await this.repository.find({
                where: { severity },
                order: { timestamp: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error('根据严重程度获取事件失败', error);
            throw error;
        }
    }
    /**
     * 根据事件类型获取事件
     */
    async findByType(eventType) {
        try {
            return await this.repository.find({
                where: { eventType },
                order: { timestamp: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error('根据事件类型获取事件失败', error);
            throw error;
        }
    }
    /**
     * 获取未解决的事件
     */
    async findUnresolved() {
        try {
            return await this.repository.find({
                where: {
                    status: security_event_entity_1.EventStatus.OPEN,
                },
                order: { severity: 'DESC', timestamp: 'DESC' },
            });
        }
        catch (error) {
            this.logger.error('获取未解决事件失败', error);
            throw error;
        }
    }
    /**
     * 更新事件状态
     */
    async updateStatus(id, status, resolvedBy) {
        try {
            const updateData = { status };
            if (status === 'resolved' && resolvedBy) {
                updateData.resolvedBy = resolvedBy;
                updateData.resolvedAt = new Date();
            }
            await this.repository.update(id, updateData);
            const updatedEvent = await this.findById(id);
            if (updatedEvent) {
                this.logger.log(`安全事件状态已更新: ${id} -> ${status}`);
            }
            return updatedEvent;
        }
        catch (error) {
            this.logger.error('更新事件状态失败', error);
            throw error;
        }
    }
    /**
     * 获取事件统计
     */
    async getStatistics(timeRange) {
        try {
            const whereCondition = {};
            if (timeRange) {
                whereCondition.timestamp = (0, typeorm_2.Between)(timeRange.start, timeRange.end);
            }
            const [total, critical, high, medium, low, open, investigating, resolved,] = await Promise.all([
                this.repository.count({ where: whereCondition }),
                this.repository.count({ where: { ...whereCondition, severity: security_event_entity_1.EventSeverity.CRITICAL } }),
                this.repository.count({ where: { ...whereCondition, severity: security_event_entity_1.EventSeverity.HIGH } }),
                this.repository.count({ where: { ...whereCondition, severity: security_event_entity_1.EventSeverity.MEDIUM } }),
                this.repository.count({ where: { ...whereCondition, severity: security_event_entity_1.EventSeverity.LOW } }),
                this.repository.count({ where: { ...whereCondition, status: security_event_entity_1.EventStatus.OPEN } }),
                this.repository.count({ where: { ...whereCondition, status: security_event_entity_1.EventStatus.INVESTIGATING } }),
                this.repository.count({ where: { ...whereCondition, status: security_event_entity_1.EventStatus.RESOLVED } }),
            ]);
            return {
                total,
                bySeverity: {
                    critical,
                    high,
                    medium,
                    low,
                },
                byStatus: {
                    open,
                    investigating,
                    resolved,
                },
                timeRange,
            };
        }
        catch (error) {
            this.logger.error('获取事件统计失败', error);
            throw error;
        }
    }
    /**
     * 分页查询事件
     */
    async findWithPagination(page = 1, limit = 10, filters) {
        try {
            const skip = (page - 1) * limit;
            const where = {};
            // 应用过滤器
            if (filters?.severity) {
                where.severity = filters.severity;
            }
            if (filters?.eventType) {
                where.eventType = filters.eventType;
            }
            if (filters?.status) {
                where.status = filters.status;
            }
            if (filters?.source) {
                where.source = filters.source;
            }
            const [events, total] = await this.repository.findAndCount({
                where,
                skip,
                take: limit,
                order: { timestamp: 'DESC' },
            });
            return {
                events,
                total,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            this.logger.error('分页查询事件失败', error);
            throw error;
        }
    }
    /**
     * 获取事件趋势数据
     */
    async getEventTrends(days = 7) {
        try {
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(endDate.getDate() - days);
            const events = await this.repository
                .createQueryBuilder('event')
                .select([
                'DATE(event.timestamp) as date',
                'event.severity',
                'COUNT(*) as count',
            ])
                .where('event.timestamp BETWEEN :start AND :end', {
                start: startDate,
                end: endDate,
            })
                .groupBy('DATE(event.timestamp), event.severity')
                .orderBy('date', 'ASC')
                .getRawMany();
            // 组织数据为趋势格式
            const trends = {};
            events.forEach(event => {
                if (!trends[event.date]) {
                    trends[event.date] = {
                        date: event.date,
                        critical: 0,
                        high: 0,
                        medium: 0,
                        low: 0,
                        total: 0,
                    };
                }
                trends[event.date][event.severity] = parseInt(event.count);
                trends[event.date].total += parseInt(event.count);
            });
            return Object.values(trends);
        }
        catch (error) {
            this.logger.error('获取事件趋势失败', error);
            throw error;
        }
    }
    /**
     * 删除旧事件
     */
    async deleteOldEvents(daysToKeep = 90) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const result = await this.repository
                .createQueryBuilder()
                .delete()
                .where('timestamp < :cutoffDate', { cutoffDate })
                .execute();
            const deletedCount = result.affected || 0;
            this.logger.log(`已删除 ${deletedCount} 个旧安全事件`);
            return deletedCount;
        }
        catch (error) {
            this.logger.error('删除旧事件失败', error);
            throw error;
        }
    }
    /**
     * 搜索事件
     */
    async search(keyword) {
        try {
            return await this.repository
                .createQueryBuilder('event')
                .where('event.description LIKE :keyword', { keyword: `%${keyword}%` })
                .orWhere('event.source LIKE :keyword', { keyword: `%${keyword}%` })
                .orWhere('event.target LIKE :keyword', { keyword: `%${keyword}%` })
                .orderBy('event.timestamp', 'DESC')
                .getMany();
        }
        catch (error) {
            this.logger.error('搜索事件失败', error);
            throw error;
        }
    }
};
exports.SecurityEventRepository = SecurityEventRepository;
exports.SecurityEventRepository = SecurityEventRepository = SecurityEventRepository_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(security_event_entity_1.SecurityEvent)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SecurityEventRepository);
