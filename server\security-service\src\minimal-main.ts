/**
 * 最小化启动文件
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { MinimalAppModule } from './minimal-app.module';

async function bootstrap() {
  const logger = new Logger('MinimalSecurityService');
  
  try {
    console.log('🚀 开始启动最小化安全服务...');
    
    // 创建应用实例
    const app = await NestFactory.create(MinimalAppModule, {
      logger: ['log', 'error', 'warn'],
    });

    console.log('✅ 应用创建成功');

    // 启用CORS
    app.enableCors();

    // 启动HTTP服务
    const port = 6666;
    const host = '127.0.0.1';

    await app.listen(port, host);

    logger.log(`🚀 最小化安全防护体系服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📊 健康检查: http://${host}:${port}/`);

  } catch (error) {
    logger.error('服务启动失败:', error);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

bootstrap();
