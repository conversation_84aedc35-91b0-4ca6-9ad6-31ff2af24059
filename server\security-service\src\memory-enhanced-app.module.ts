/**
 * 内存增强应用模块
 * 使用内存存储的完整安全服务（不依赖数据库）
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TerminusModule } from '@nestjs/terminus';

// 控制器
import { SimpleController } from './simple-controller';
import { EnhancedSecurityController } from './enhanced-security.controller';

// 服务
import { EnhancedSecurityService } from './enhanced-security.service';

// 健康检查
import { HealthController } from './health/health.controller';

// 内存存储服务
import { MemoryStorageService } from './memory-storage.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // JWT模块
    JwtModule.register({
      secret: 'security-service-jwt-secret-key-change-in-production',
      signOptions: {
        expiresIn: '24h',
      },
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 健康检查模块
    TerminusModule,
  ],
  controllers: [
    SimpleController,
    EnhancedSecurityController,
    HealthController,
  ],
  providers: [
    // 内存存储服务
    MemoryStorageService,
    
    // 业务服务
    {
      provide: EnhancedSecurityService,
      useClass: EnhancedSecurityService,
    },
  ],
  exports: [
    MemoryStorageService,
    EnhancedSecurityService,
  ],
})
export class MemoryEnhancedAppModule {
  constructor() {
    console.log('🔐 内存增强安全防护体系服务模块已加载');
    console.log('✅ 内存存储已启用');
    console.log('✅ 核心安全功能已集成');
    console.log('✅ 零信任架构已配置');
    console.log('✅ 威胁检测已启用');
  }
}
