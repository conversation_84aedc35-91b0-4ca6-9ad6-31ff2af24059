"use strict";
/**
 * 数据库健康检查指示器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const database_service_1 = require("./database.service");
let DatabaseHealthIndicator = class DatabaseHealthIndicator extends terminus_1.HealthIndicator {
    constructor(databaseService) {
        super();
        this.databaseService = databaseService;
    }
    async isHealthy(key) {
        try {
            const healthStatus = await this.databaseService.getHealthStatus();
            if (healthStatus.status === 'healthy' || healthStatus.status === 'disabled') {
                return this.getStatus(key, true, healthStatus);
            }
            else {
                throw new terminus_1.HealthCheckError('数据库健康检查失败', this.getStatus(key, false, healthStatus));
            }
        }
        catch (error) {
            throw new terminus_1.HealthCheckError('数据库健康检查异常', this.getStatus(key, false, {
                status: 'error',
                message: error.message,
            }));
        }
    }
};
exports.DatabaseHealthIndicator = DatabaseHealthIndicator;
exports.DatabaseHealthIndicator = DatabaseHealthIndicator = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [database_service_1.DatabaseService])
], DatabaseHealthIndicator);
