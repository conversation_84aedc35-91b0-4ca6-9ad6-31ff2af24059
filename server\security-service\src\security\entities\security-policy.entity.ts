/**
 * 安全策略实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum PolicyType {
  ACCESS_CONTROL = 'access_control',
  DATA_PROTECTION = 'data_protection',
  NETWORK_SECURITY = 'network_security',
  DEVICE_COMPLIANCE = 'device_compliance',
  ZERO_TRUST = 'zero_trust',
}

export enum EnforcementMode {
  MONITOR = 'monitor',
  ENFORCE = 'enforce',
  BLOCK = 'block',
}

export enum PolicyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
  ARCHIVED = 'archived',
}

@Entity('security_policies')
@Index(['policyType', 'status'])
@Index(['createdBy'])
@Index(['priority'])
export class SecurityPolicy {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  policyId: string;

  @Column()
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PolicyType,
    default: PolicyType.ACCESS_CONTROL,
  })
  policyType: PolicyType;

  @Column({
    type: 'enum',
    enum: EnforcementMode,
    default: EnforcementMode.MONITOR,
  })
  enforcementMode: EnforcementMode;

  @Column({
    type: 'enum',
    enum: PolicyStatus,
    default: PolicyStatus.DRAFT,
  })
  status: PolicyStatus;

  @Column({ type: 'int', default: 5 })
  priority: number;

  @Column('json', { nullable: true })
  scope: {
    users: string[];
    devices: string[];
    applications: string[];
    networks: string[];
    resources: string[];
    timeWindows: any[];
    locations: any[];
  };

  @Column('json', { nullable: true })
  rules: {
    ruleId: string;
    type: string;
    condition: any;
    action: any;
    weight: number;
  }[];

  @Column('json', { nullable: true })
  conditions: {
    userAttributes: any;
    deviceAttributes: any;
    networkAttributes: any;
    contextAttributes: any;
    riskScore: number;
    trustScore: number;
  };

  @Column('json', { nullable: true })
  actions: {
    type: string;
    parameters: any;
    notifications: string[];
    logging: boolean;
  };

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;

  @Column({ type: 'datetime', nullable: true })
  effectiveFrom: Date;

  @Column({ type: 'datetime', nullable: true })
  effectiveTo: Date;

  @Column({ type: 'int', default: 0 })
  violationCount: number;

  @Column({ type: 'datetime', nullable: true })
  lastViolation: Date;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column('json', { nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
