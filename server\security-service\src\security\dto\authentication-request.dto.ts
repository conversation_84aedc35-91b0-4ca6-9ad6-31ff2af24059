/**
 * 身份验证请求DTO
 */

import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  IsObject,
  IsArray,
  ValidateNested,
  IsIP,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum AuthenticationMethodDto {
  PASSWORD = 'password',
  MFA = 'mfa',
  BIOMETRIC = 'biometric',
  CERTIFICATE = 'certificate',
  TOKEN = 'token',
  SSO = 'sso',
  OAUTH2 = 'oauth2',
  SAML = 'saml',
}

export class GeographicLocationDto {
  @ApiProperty({ description: '纬度' })
  @IsNumber()
  latitude: number;

  @ApiProperty({ description: '经度' })
  @IsNumber()
  longitude: number;

  @ApiPropertyOptional({ description: '半径（米）' })
  @IsOptional()
  @IsNumber()
  radius?: number;

  @ApiPropertyOptional({ description: '位置名称' })
  @IsOptional()
  @IsString()
  name?: string;
}

export class DeviceInfoDto {
  @ApiProperty({ description: '设备ID' })
  @IsString()
  deviceId: string;

  @ApiPropertyOptional({ description: '设备类型' })
  @IsOptional()
  @IsString()
  deviceType?: string;

  @ApiPropertyOptional({ description: '设备名称' })
  @IsOptional()
  @IsString()
  deviceName?: string;

  @ApiPropertyOptional({ description: '操作系统' })
  @IsOptional()
  @IsString()
  operatingSystem?: string;

  @ApiPropertyOptional({ description: '操作系统版本' })
  @IsOptional()
  @IsString()
  osVersion?: string;

  @ApiPropertyOptional({ description: '浏览器' })
  @IsOptional()
  @IsString()
  browser?: string;

  @ApiPropertyOptional({ description: '浏览器版本' })
  @IsOptional()
  @IsString()
  browserVersion?: string;

  @ApiPropertyOptional({ description: '设备指纹' })
  @IsOptional()
  @IsString()
  fingerprint?: string;

  @ApiPropertyOptional({ description: '是否受信任设备' })
  @IsOptional()
  isTrusted?: boolean;
}

export class AuthenticationRequestDto {
  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '用户名' })
  @IsString()
  username: string;

  @ApiPropertyOptional({ description: '密码（如果使用密码认证）' })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    enum: AuthenticationMethodDto,
    description: '认证方式',
  })
  @IsEnum(AuthenticationMethodDto)
  authMethod: AuthenticationMethodDto;

  @ApiProperty({ description: 'IP地址' })
  @IsIP()
  ipAddress: string;

  @ApiProperty({ description: '用户代理' })
  @IsString()
  userAgent: string;

  @ApiPropertyOptional({ type: DeviceInfoDto, description: '设备信息' })
  @IsOptional()
  @ValidateNested()
  @Type(() => DeviceInfoDto)
  deviceInfo?: DeviceInfoDto;

  @ApiPropertyOptional({ type: GeographicLocationDto, description: '地理位置' })
  @IsOptional()
  @ValidateNested()
  @Type(() => GeographicLocationDto)
  location?: GeographicLocationDto;

  @ApiPropertyOptional({ description: '会话ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: '请求的资源' })
  @IsOptional()
  @IsString()
  requestedResource?: string;

  @ApiPropertyOptional({ description: '请求的权限' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requestedPermissions?: string[];

  @ApiPropertyOptional({ description: '多因素认证令牌' })
  @IsOptional()
  @IsString()
  mfaToken?: string;

  @ApiPropertyOptional({ description: '生物识别数据' })
  @IsOptional()
  @IsString()
  biometricData?: string;

  @ApiPropertyOptional({ description: '客户端证书' })
  @IsOptional()
  @IsString()
  clientCertificate?: string;

  @ApiPropertyOptional({ description: 'OAuth2访问令牌' })
  @IsOptional()
  @IsString()
  accessToken?: string;

  @ApiPropertyOptional({ description: 'SAML断言' })
  @IsOptional()
  @IsString()
  samlAssertion?: string;

  @ApiPropertyOptional({ description: '上下文信息' })
  @IsOptional()
  @IsObject()
  context?: {
    applicationId?: string;
    requestTime?: Date;
    referrer?: string;
    riskScore?: number;
    previousFailedAttempts?: number;
    lastSuccessfulLogin?: Date;
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: '额外元数据' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
