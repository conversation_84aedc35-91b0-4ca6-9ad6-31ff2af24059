"use strict";
/**
 * 认证服务
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = __importStar(require("bcrypt"));
const crypto = __importStar(require("crypto"));
const user_entity_1 = require("./entities/user.entity");
const memory_storage_service_1 = require("../memory-storage.service");
let AuthService = AuthService_1 = class AuthService {
    constructor(jwtService, configService, memoryStorageService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.memoryStorageService = memoryStorageService;
        this.logger = new common_1.Logger(AuthService_1.name);
        this.users = new Map();
        this.sessions = new Map();
        this.accessLogs = new Map();
        this.refreshTokens = new Map();
        this.initializeDefaultUsers();
    }
    /**
     * 初始化默认用户
     */
    async initializeDefaultUsers() {
        try {
            // 创建默认管理员用户
            const adminExists = Array.from(this.users.values()).some(user => user.role === user_entity_1.UserRole.ADMIN);
            if (!adminExists) {
                const adminUser = new user_entity_1.User({
                    username: 'admin',
                    email: '<EMAIL>',
                    passwordHash: await this.hashPassword('admin123'),
                    salt: await this.generateSalt(),
                    role: user_entity_1.UserRole.ADMIN,
                    status: user_entity_1.UserStatus.ACTIVE,
                    permissions: [
                        { resource: '*', actions: ['*'] }, // 管理员拥有所有权限
                    ],
                    profile: {
                        firstName: 'System',
                        lastName: 'Administrator',
                        department: 'Security',
                        position: 'System Administrator',
                    },
                });
                this.users.set(adminUser.id, adminUser);
                this.logger.log('默认管理员用户已创建: admin/admin123');
            }
            // 创建默认安全分析师用户
            const analystExists = Array.from(this.users.values()).some(user => user.username === 'analyst');
            if (!analystExists) {
                const analystUser = new user_entity_1.User({
                    username: 'analyst',
                    email: '<EMAIL>',
                    passwordHash: await this.hashPassword('analyst123'),
                    salt: await this.generateSalt(),
                    role: user_entity_1.UserRole.SECURITY_ANALYST,
                    status: user_entity_1.UserStatus.ACTIVE,
                    permissions: [
                        { resource: 'security', actions: ['read', 'write', 'analyze'] },
                        { resource: 'threats', actions: ['read', 'detect', 'respond'] },
                        { resource: 'policies', actions: ['read', 'write'] },
                        { resource: 'audit', actions: ['read'] },
                    ],
                    profile: {
                        firstName: 'Security',
                        lastName: 'Analyst',
                        department: 'Security',
                        position: 'Security Analyst',
                    },
                });
                this.users.set(analystUser.id, analystUser);
                this.logger.log('默认安全分析师用户已创建: analyst/analyst123');
            }
            this.logger.log(`已初始化 ${this.users.size} 个用户`);
        }
        catch (error) {
            this.logger.error('初始化默认用户失败', error);
        }
    }
    /**
     * 用户登录
     */
    async login(loginDto, clientIp, userAgent) {
        try {
            // 查找用户
            const user = this.findUserByUsernameOrEmail(loginDto.username);
            if (!user) {
                throw new common_1.UnauthorizedException('用户名或密码错误');
            }
            // 检查用户状态
            if (!user.canAccess()) {
                throw new common_1.UnauthorizedException('用户账户被禁用或锁定');
            }
            // 验证密码
            const isPasswordValid = await this.verifyPassword(loginDto.password, user.passwordHash);
            if (!isPasswordValid) {
                user.incrementLoginAttempts();
                await this.recordAccessLog(user.id, {
                    ip: clientIp,
                    userAgent,
                    endpoint: '/auth/login',
                    method: 'POST',
                    timestamp: new Date(),
                    success: false,
                    errorMessage: '密码错误',
                });
                throw new common_1.UnauthorizedException('用户名或密码错误');
            }
            // 检查是否需要MFA
            if (user.securitySettings.mfaEnabled && !loginDto.mfaCode) {
                // 生成MFA挑战令牌
                const mfaChallengeToken = this.generateMfaChallengeToken(user.id);
                return {
                    accessToken: '',
                    refreshToken: '',
                    tokenType: 'Bearer',
                    expiresIn: 0,
                    user: null,
                    permissions: [],
                    requiresMfa: true,
                    mfaChallengeToken,
                };
            }
            // 验证MFA（如果提供了）
            if (user.securitySettings.mfaEnabled && loginDto.mfaCode) {
                const isMfaValid = await this.verifyMfaCode(user.id, loginDto.mfaCode);
                if (!isMfaValid) {
                    throw new common_1.UnauthorizedException('MFA验证码错误');
                }
            }
            // 创建会话
            const session = await this.createSession(user.id, loginDto.deviceId, clientIp, userAgent);
            // 生成JWT令牌
            const tokens = await this.generateTokens(user, session.id, loginDto.deviceId);
            // 更新用户登录信息
            user.updateLastLogin(clientIp);
            // 记录成功登录日志
            await this.recordAccessLog(user.id, {
                ip: clientIp,
                userAgent,
                endpoint: '/auth/login',
                method: 'POST',
                timestamp: new Date(),
                success: true,
            });
            this.logger.log(`用户登录成功: ${user.username} (${clientIp})`);
            return {
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
                tokenType: 'Bearer',
                expiresIn: 3600, // 1小时
                user: user.toSafeObject(),
                permissions: user.permissions.map(p => `${p.resource}:${p.actions.join(',')}`),
            };
        }
        catch (error) {
            this.logger.error('用户登录失败', error);
            throw error;
        }
    }
    /**
     * 用户注册
     */
    async register(registerDto) {
        try {
            // 检查用户名是否已存在
            if (this.findUserByUsernameOrEmail(registerDto.username)) {
                throw new common_1.ConflictException('用户名已存在');
            }
            // 检查邮箱是否已存在
            if (this.findUserByUsernameOrEmail(registerDto.email)) {
                throw new common_1.ConflictException('邮箱已存在');
            }
            // 验证密码确认
            if (registerDto.password !== registerDto.confirmPassword) {
                throw new common_1.BadRequestException('密码确认不匹配');
            }
            // 创建新用户
            const user = new user_entity_1.User({
                username: registerDto.username,
                email: registerDto.email,
                passwordHash: await this.hashPassword(registerDto.password),
                salt: await this.generateSalt(),
                role: registerDto.role || user_entity_1.UserRole.VIEWER,
                status: user_entity_1.UserStatus.PENDING, // 新用户需要激活
                permissions: this.getDefaultPermissions(registerDto.role || user_entity_1.UserRole.VIEWER),
                profile: {
                    firstName: registerDto.firstName,
                    lastName: registerDto.lastName,
                    department: registerDto.department,
                    position: registerDto.position,
                },
            });
            this.users.set(user.id, user);
            this.logger.log(`新用户注册: ${user.username}`);
            return user;
        }
        catch (error) {
            this.logger.error('用户注册失败', error);
            throw error;
        }
    }
    /**
     * 验证用户ID
     */
    async validateUserById(userId) {
        return this.users.get(userId) || null;
    }
    /**
     * 验证会话
     */
    async validateSession(sessionId, userId) {
        const session = this.sessions.get(sessionId);
        return session &&
            session.userId === userId &&
            session.isActive &&
            session.expiresAt > new Date();
    }
    /**
     * 记录访问日志
     */
    async recordAccessLog(userId, logData) {
        const log = {
            id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            userId,
            ip: logData.ip || '127.0.0.1',
            userAgent: logData.userAgent || 'Unknown',
            endpoint: logData.endpoint || '',
            method: logData.method || 'GET',
            timestamp: logData.timestamp || new Date(),
            success: logData.success !== undefined ? logData.success : true,
            errorMessage: logData.errorMessage,
        };
        this.accessLogs.set(log.id, log);
    }
    /**
     * 用户登出
     */
    async logout(sessionId) {
        try {
            const session = this.sessions.get(sessionId);
            if (session) {
                session.isActive = false;
                this.logger.log(`用户登出: 会话 ${sessionId}`);
            }
        }
        catch (error) {
            this.logger.error('用户登出失败', error);
            throw error;
        }
    }
    /**
     * 获取所有用户
     */
    async getAllUsers() {
        return Array.from(this.users.values()).map(user => user);
    }
    /**
     * 根据ID获取用户
     */
    async getUserById(userId) {
        return this.users.get(userId) || null;
    }
    /**
     * 更新用户状态
     */
    async updateUserStatus(userId, status) {
        const user = this.users.get(userId);
        if (user) {
            user.status = status;
            user.updatedAt = new Date();
            return user;
        }
        return null;
    }
    // ==================== 私有辅助方法 ====================
    findUserByUsernameOrEmail(usernameOrEmail) {
        return Array.from(this.users.values()).find(user => user.username === usernameOrEmail || user.email === usernameOrEmail) || null;
    }
    async hashPassword(password) {
        const saltRounds = 12;
        return bcrypt.hash(password, saltRounds);
    }
    async verifyPassword(password, hash) {
        return bcrypt.compare(password, hash);
    }
    async generateSalt() {
        return crypto.randomBytes(32).toString('hex');
    }
    async generateTokens(user, sessionId, deviceId) {
        const payload = {
            sub: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            permissions: user.permissions.map(p => `${p.resource}:${p.actions.join(',')}`),
            sessionId,
            deviceId,
        };
        const accessToken = this.jwtService.sign(payload, { expiresIn: '1h' });
        const refreshToken = crypto.randomBytes(32).toString('hex');
        // 存储刷新令牌
        this.refreshTokens.set(refreshToken, {
            userId: user.id,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
        });
        return { accessToken, refreshToken };
    }
    async createSession(userId, deviceId, ip, userAgent) {
        const session = {
            id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            userId,
            deviceId,
            ip,
            userAgent,
            createdAt: new Date(),
            lastAccessAt: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
            isActive: true,
        };
        this.sessions.set(session.id, session);
        return session;
    }
    generateMfaChallengeToken(userId) {
        // 简化的MFA挑战令牌生成
        return `mfa_${userId}_${Date.now()}`;
    }
    async verifyMfaCode(userId, code) {
        // 简化的MFA验证（实际应该验证TOTP、SMS等）
        return code === '123456'; // 演示用固定验证码
    }
    getDefaultPermissions(role) {
        switch (role) {
            case user_entity_1.UserRole.ADMIN:
                return [{ resource: '*', actions: ['*'] }];
            case user_entity_1.UserRole.SECURITY_ANALYST:
                return [
                    { resource: 'security', actions: ['read', 'write', 'analyze'] },
                    { resource: 'threats', actions: ['read', 'detect', 'respond'] },
                    { resource: 'policies', actions: ['read', 'write'] },
                    { resource: 'audit', actions: ['read'] },
                ];
            case user_entity_1.UserRole.OPERATOR:
                return [
                    { resource: 'security', actions: ['read', 'write'] },
                    { resource: 'threats', actions: ['read', 'respond'] },
                    { resource: 'policies', actions: ['read'] },
                ];
            case user_entity_1.UserRole.VIEWER:
                return [
                    { resource: 'security', actions: ['read'] },
                    { resource: 'threats', actions: ['read'] },
                    { resource: 'policies', actions: ['read'] },
                ];
            default:
                return [{ resource: 'security', actions: ['read'] }];
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService,
        memory_storage_service_1.MemoryStorageService])
], AuthService);
