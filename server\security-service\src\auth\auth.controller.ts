/**
 * 认证控制器
 */

import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  Req,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { Request } from 'express';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';
import {
  LoginDto,
  RegisterDto,
  ChangePasswordDto,
  UpdateProfileDto,
  UpdateSecuritySettingsDto,
  LoginResponseDto,
  UserResponseDto,
} from './dto/auth.dto';
import {
  Public,
  CurrentUser,
  UserId,
  ClientIp,
  AdminOnly,
  SecurityAnalystOrAbove,
  UserManagement,
} from './decorators/auth.decorators';
import { UserStatus } from './entities/user.entity';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(private authService: AuthService) {}

  // ==================== 公开端点 ====================

  @Post('login')
  @Public()
  @ApiOperation({ summary: '用户登录' })
  @ApiResponse({ status: 200, description: '登录成功', type: LoginResponseDto })
  @ApiResponse({ status: 401, description: '认证失败' })
  async login(
    @Body() loginDto: LoginDto,
    @Req() req: Request,
  ): Promise<any> {
    try {
      const clientIp = this.getClientIp(req);
      const userAgent = req.headers['user-agent'] || 'Unknown';

      this.logger.log(`登录尝试: ${loginDto.username} from ${clientIp}`);

      const result = await this.authService.login(loginDto, clientIp, userAgent);

      return {
        success: true,
        data: result,
        message: result.requiresMfa ? '需要MFA验证' : '登录成功',
      };
    } catch (error) {
      this.logger.error('登录失败', error);
      throw new HttpException(
        {
          success: false,
          message: '登录失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  @Post('register')
  @Public()
  @ApiOperation({ summary: '用户注册' })
  @ApiResponse({ status: 201, description: '注册成功', type: UserResponseDto })
  @ApiResponse({ status: 400, description: '注册失败' })
  async register(@Body() registerDto: RegisterDto): Promise<any> {
    try {
      this.logger.log(`注册尝试: ${registerDto.username}`);

      const user = await this.authService.register(registerDto);

      return {
        success: true,
        data: user.toSafeObject(),
        message: '注册成功，等待管理员激活',
      };
    } catch (error) {
      this.logger.error('注册失败', error);
      throw new HttpException(
        {
          success: false,
          message: '注册失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // ==================== 认证端点 ====================

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '用户登出' })
  @ApiResponse({ status: 200, description: '登出成功' })
  async logout(@CurrentUser('sessionId') sessionId: string): Promise<any> {
    try {
      await this.authService.logout(sessionId);

      return {
        success: true,
        message: '登出成功',
      };
    } catch (error) {
      this.logger.error('登出失败', error);
      throw new HttpException(
        {
          success: false,
          message: '登出失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取用户资料' })
  @ApiResponse({ status: 200, description: '用户资料', type: UserResponseDto })
  async getProfile(@UserId() userId: string): Promise<any> {
    try {
      const user = await this.authService.getUserById(userId);
      if (!user) {
        throw new HttpException('用户不存在', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: user.toSafeObject(),
        message: '获取用户资料成功',
      };
    } catch (error) {
      this.logger.error('获取用户资料失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取用户资料失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== 辅助方法 ====================

  private getClientIp(req: Request): string {
    return req.headers['x-forwarded-for'] as string ||
           req.headers['x-real-ip'] as string ||
           req.connection?.remoteAddress ||
           req.socket?.remoteAddress ||
           req.ip ||
           '127.0.0.1';
  }
}
