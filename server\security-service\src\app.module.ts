/**
 * 安全防护体系服务应用模块
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
// import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TerminusModule } from '@nestjs/terminus';

// 应用核心
import { AppController } from './app.controller';
import { AppService } from './app.service';

// 安全模块
import { SecurityModule } from './security/security.module';
import { AuthModule } from './auth/auth.module';
import { EncryptionModule } from './encryption/encryption.module';
import { BlockchainModule } from './blockchain/blockchain.module';
import { PrivacyModule } from './privacy/privacy.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { ComplianceModule } from './compliance/compliance.module';

// 健康检查模块
import { HealthModule } from './health/health.module';

// 公共模块
// import { CommonModule } from './common/common.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块（暂时禁用）
    // TypeOrmModule.forRootAsync({...}),

    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET', 'security-service-secret'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '24h'),
        },
      }),
      inject: [ConfigService],
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 调度模块
    ScheduleModule.forRoot(),

    // 健康检查模块
    TerminusModule,

    // 微服务客户端模块
    ClientsModule.registerAsync([
      {
        name: 'USER_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('USER_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('USER_SERVICE_PORT', 3002),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'AUDIT_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('AUDIT_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('AUDIT_SERVICE_PORT', 3030),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: 'NOTIFICATION_SERVICE',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('NOTIFICATION_SERVICE_HOST', 'localhost'),
            port: configService.get<number>('NOTIFICATION_SERVICE_PORT', 3040),
          },
        }),
        inject: [ConfigService],
      },
    ]),

    // 公共模块（暂时禁用）
    // CommonModule,

    // 核心安全模块
    SecurityModule,
    AuthModule,
    EncryptionModule,
    BlockchainModule,
    PrivacyModule,
    MonitoringModule,
    ComplianceModule,

    // 健康检查模块
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 启动时日志
    console.log('🔐 安全防护体系服务模块已加载');
    console.log(`📊 数据库: ${this.configService.get('DB_HOST')}:${this.configService.get('DB_PORT')}`);
    console.log(`🔑 JWT过期时间: ${this.configService.get('JWT_EXPIRES_IN')}`);
    console.log(`🌍 环境: ${this.configService.get('NODE_ENV')}`);
  }
}
