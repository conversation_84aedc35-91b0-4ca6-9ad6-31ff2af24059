/**
 * 数据库健康检查指示器
 */

import { Injectable } from '@nestjs/common';
import { HealthIndicator, HealthIndicatorResult, HealthCheckError } from '@nestjs/terminus';
import { DatabaseService } from './database.service';

@Injectable()
export class DatabaseHealthIndicator extends HealthIndicator {
  constructor(private databaseService: DatabaseService) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    try {
      const healthStatus = await this.databaseService.getHealthStatus();
      
      if (healthStatus.status === 'healthy' || healthStatus.status === 'disabled') {
        return this.getStatus(key, true, healthStatus);
      } else {
        throw new HealthCheckError('数据库健康检查失败', this.getStatus(key, false, healthStatus));
      }
    } catch (error) {
      throw new HealthCheckError('数据库健康检查异常', this.getStatus(key, false, {
        status: 'error',
        message: error.message,
      }));
    }
  }
}
