/**
 * 用户会话实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('user_sessions')
@Index(['userId'])
@Index(['sessionId'])
@Index(['status'])
@Index(['createdAt'])
export class UserSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  sessionId: string;

  @Column()
  userId: string;

  @Column({ nullable: true })
  deviceId: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column('text', { nullable: true })
  userAgent: string;

  @Column({ default: 'active' })
  status: string;

  @Column({ type: 'int', default: 100 })
  trustScore: number;

  @Column({ type: 'int', default: 0 })
  riskScore: number;

  @Column('json', { nullable: true })
  metadata: any;

  @Column({ type: 'datetime', nullable: true })
  lastActivity: Date;

  @Column({ type: 'datetime', nullable: true })
  expiresAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
