/**
 * Security-Service 项目结构验证脚本
 * 
 * 验证项目是否包含所有必需的文件和配置
 */

const fs = require('fs');
const path = require('path');

// 必需的文件和目录
const requiredStructure = {
  files: [
    // 根配置文件
    'package.json',
    'tsconfig.json',
    'nest-cli.json',
    '.env.example',
    'Dockerfile',
    'docker-compose.yml',
    'README.md',
    
    // 应用核心文件
    'src/main.ts',
    'src/app.module.ts',
    'src/app.controller.ts',
    'src/app.service.ts',
    
    // 安全模块
    'src/security/security.module.ts',
    'src/security/security.controller.ts',
    'src/security/security.service.ts',
    'src/security/zero-trust-security.service.ts',
    
    // 实体定义
    'src/security/entities/security-policy.entity.ts',
    'src/security/entities/security-event.entity.ts',
    
    // DTO定义
    'src/security/dto/create-security-policy.dto.ts',
    'src/security/dto/authentication-request.dto.ts',
    
    // 验证脚本
    'scripts/verify-project.js',
  ],
  
  directories: [
    'src',
    'src/security',
    'src/security/entities',
    'src/security/dto',
    'src/security/guards',
    'src/security/strategies',
    'src/security/middleware',
    'src/auth',
    'src/encryption',
    'src/blockchain',
    'src/privacy',
    'src/monitoring',
    'src/compliance',
    'src/health',
    'src/common',
    'scripts',
    'test',
    'logs',
    'config',
  ]
};

let allValid = true;
let validCount = 0;
let totalCount = requiredStructure.files.length + requiredStructure.directories.length;

console.log('🔐 验证安全防护体系服务项目结构...');
console.log('='.repeat(60));

// 检查文件
console.log('\n📁 检查必需文件:');
console.log('-'.repeat(40));

requiredStructure.files.forEach(filePath => {
  const fullPath = path.join(__dirname, '..', filePath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isFile();
  
  if (exists) {
    console.log(`✅ ${filePath}`);
    validCount++;
  } else {
    console.log(`❌ ${filePath} - 文件缺失`);
    allValid = false;
  }
});

// 检查目录
console.log('\n📂 检查必需目录:');
console.log('-'.repeat(40));

requiredStructure.directories.forEach(dirPath => {
  const fullPath = path.join(__dirname, '..', dirPath);
  const exists = fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory();
  
  if (exists) {
    console.log(`✅ ${dirPath}/`);
    validCount++;
  } else {
    console.log(`❌ ${dirPath}/ - 目录缺失`);
    allValid = false;
  }
});

console.log('\n' + '='.repeat(60));
console.log(`📊 验证结果: ${validCount}/${totalCount} 项目结构完整`);

// 检查package.json内容
console.log('\n📦 检查 package.json 配置:');
console.log('-'.repeat(40));

try {
  const packagePath = path.join(__dirname, '..', 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // 检查基本信息
    const requiredFields = ['name', 'version', 'description', 'main', 'scripts'];
    requiredFields.forEach(field => {
      if (packageJson[field]) {
        console.log(`✅ ${field}: ${typeof packageJson[field] === 'object' ? 'configured' : packageJson[field]}`);
      } else {
        console.log(`❌ ${field}: 缺失`);
        allValid = false;
      }
    });
    
    // 检查关键依赖
    const requiredDeps = [
      '@nestjs/common',
      '@nestjs/core',
      '@nestjs/platform-express',
      '@nestjs/typeorm',
      '@nestjs/config',
      '@nestjs/swagger',
      'typeorm',
      'mysql2',
      'redis',
      'bcrypt',
      'jsonwebtoken',
      'helmet',
      'class-validator',
      'class-transformer'
    ];
    
    console.log('\n🔗 检查关键依赖:');
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`❌ ${dep}: 缺失`);
        allValid = false;
      }
    });
    
  } else {
    console.log('❌ package.json 文件不存在');
    allValid = false;
  }
} catch (error) {
  console.log(`❌ 解析 package.json 失败: ${error.message}`);
  allValid = false;
}

// 检查TypeScript配置
console.log('\n🔧 检查 TypeScript 配置:');
console.log('-'.repeat(40));

try {
  const tsconfigPath = path.join(__dirname, '..', 'tsconfig.json');
  if (fs.existsSync(tsconfigPath)) {
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    
    if (tsconfig.compilerOptions) {
      console.log('✅ TypeScript 编译选项已配置');
      
      const requiredOptions = ['target', 'module', 'outDir', 'experimentalDecorators', 'emitDecoratorMetadata'];
      requiredOptions.forEach(option => {
        if (tsconfig.compilerOptions[option] !== undefined) {
          console.log(`✅ ${option}: ${tsconfig.compilerOptions[option]}`);
        } else {
          console.log(`⚠️ ${option}: 未配置`);
        }
      });
    } else {
      console.log('❌ TypeScript 编译选项缺失');
      allValid = false;
    }
  } else {
    console.log('❌ tsconfig.json 文件不存在');
    allValid = false;
  }
} catch (error) {
  console.log(`❌ 解析 tsconfig.json 失败: ${error.message}`);
  allValid = false;
}

// 检查环境配置
console.log('\n🌍 检查环境配置:');
console.log('-'.repeat(40));

const envExamplePath = path.join(__dirname, '..', '.env.example');
if (fs.existsSync(envExamplePath)) {
  console.log('✅ .env.example 文件存在');
  
  try {
    const envContent = fs.readFileSync(envExamplePath, 'utf8');
    const requiredEnvVars = [
      'NODE_ENV',
      'PORT',
      'DB_HOST',
      'DB_PORT',
      'DB_USERNAME',
      'DB_PASSWORD',
      'DB_DATABASE',
      'JWT_SECRET',
      'REDIS_HOST',
      'REDIS_PORT'
    ];
    
    requiredEnvVars.forEach(envVar => {
      if (envContent.includes(envVar)) {
        console.log(`✅ ${envVar}: 已配置`);
      } else {
        console.log(`❌ ${envVar}: 缺失`);
        allValid = false;
      }
    });
  } catch (error) {
    console.log(`❌ 读取 .env.example 失败: ${error.message}`);
    allValid = false;
  }
} else {
  console.log('❌ .env.example 文件不存在');
  allValid = false;
}

// 最终结果
console.log('\n' + '='.repeat(60));
if (allValid) {
  console.log('🎉 项目结构验证通过！安全防护体系服务已准备就绪。');
  console.log('\n📋 下一步操作:');
  console.log('1. 复制 .env.example 到 .env 并配置环境变量');
  console.log('2. 安装依赖: npm install');
  console.log('3. 启动数据库服务');
  console.log('4. 运行数据库迁移');
  console.log('5. 启动服务: npm run start:dev');
  process.exit(0);
} else {
  console.log('❌ 项目结构验证失败！请修复上述问题后重新验证。');
  console.log('\n🔧 修复建议:');
  console.log('1. 确保所有必需文件和目录存在');
  console.log('2. 检查 package.json 配置');
  console.log('3. 验证 TypeScript 配置');
  console.log('4. 确认环境变量配置');
  process.exit(1);
}
