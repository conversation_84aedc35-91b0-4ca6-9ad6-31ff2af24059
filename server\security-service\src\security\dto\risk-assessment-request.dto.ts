/**
 * 风险评估请求DTO
 */

import { IsString, IsOptional, IsEnum, IsArray, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum AssessmentTypeDto {
  SECURITY = 'security',
  COMPLIANCE = 'compliance',
  OPERATIONAL = 'operational',
  FINANCIAL = 'financial',
  COMPREHENSIVE = 'comprehensive',
}

export class RiskAssessmentRequestDto {
  @ApiProperty({ description: '评估目标' })
  @IsString()
  target: string;

  @ApiPropertyOptional({
    enum: AssessmentTypeDto,
    description: '评估类型',
    default: AssessmentTypeDto.SECURITY,
  })
  @IsOptional()
  @IsEnum(AssessmentTypeDto)
  assessmentType?: AssessmentTypeDto;

  @ApiPropertyOptional({ type: [String], description: '评估范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  scope?: string[];

  @ApiPropertyOptional({ description: '评估标准' })
  @IsOptional()
  @IsString()
  standard?: string;

  @ApiPropertyOptional({ description: '上下文信息' })
  @IsOptional()
  @IsObject()
  context?: any;
}
