/**
 * 设备信任实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('device_trust')
@Index(['deviceId'])
@Index(['trustScore'])
@Index(['complianceStatus'])
export class DeviceTrust {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  deviceId: string;

  @Column({ type: 'int', default: 0 })
  trustScore: number;

  @Column({ default: 'unknown' })
  complianceStatus: string;

  @Column('json', { nullable: true })
  securityPosture: any;

  @Column({ type: 'datetime', nullable: true })
  lastAssessment: Date;

  @Column('json', { nullable: true })
  certificates: any;

  @Column('json', { nullable: true })
  vulnerabilities: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
