/**
 * 认证相关装饰器
 */

import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { UserRole as UserRoleEnum } from '../entities/user.entity';
import { ROLES_KEY } from '../guards/roles.guard';
import { PERMISSIONS_KEY, RequiredPermission } from '../guards/permissions.guard';

/**
 * 角色装饰器
 */
export const Roles = (...roles: UserRoleEnum[]) => SetMetadata(ROLES_KEY, roles);

/**
 * 权限装饰器
 */
export const RequirePermissions = (...permissions: RequiredPermission[]) => 
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * 公开路由装饰器（不需要认证）
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * 获取当前用户装饰器
 */
export const CurrentUser = createParamDecorator(
  (data: string, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    return data ? user?.[data] : user;
  },
);

/**
 * 获取用户ID装饰器
 */
export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.userId;
  },
);

/**
 * 获取用户角色装饰器
 */
export const UserRole = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.role;
  },
);

/**
 * 获取客户端IP装饰器
 */
export const ClientIp = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.clientIp || 
           request.headers['x-forwarded-for'] || 
           request.headers['x-real-ip'] || 
           request.connection?.remoteAddress || 
           request.socket?.remoteAddress ||
           request.ip ||
           '127.0.0.1';
  },
);

/**
 * 获取设备ID装饰器
 */
export const DeviceId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.deviceId;
  },
);

/**
 * 获取会话ID装饰器
 */
export const SessionId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.sessionId;
  },
);

/**
 * 管理员权限装饰器
 */
export const AdminOnly = () => Roles(UserRoleEnum.ADMIN);

/**
 * 安全分析师权限装饰器
 */
export const SecurityAnalystOrAbove = () => Roles(UserRoleEnum.ADMIN, UserRoleEnum.SECURITY_ANALYST);

/**
 * 操作员权限装饰器
 */
export const OperatorOrAbove = () => Roles(UserRoleEnum.ADMIN, UserRoleEnum.SECURITY_ANALYST, UserRoleEnum.OPERATOR);

/**
 * 查看者权限装饰器
 */
export const ViewerOrAbove = () => Roles(UserRoleEnum.ADMIN, UserRoleEnum.SECURITY_ANALYST, UserRoleEnum.OPERATOR, UserRoleEnum.VIEWER);

/**
 * 安全管理权限装饰器
 */
export const SecurityManagement = () => RequirePermissions(
  { resource: 'security', action: 'manage' }
);

/**
 * 安全读取权限装饰器
 */
export const SecurityRead = () => RequirePermissions(
  { resource: 'security', action: 'read' }
);

/**
 * 安全写入权限装饰器
 */
export const SecurityWrite = () => RequirePermissions(
  { resource: 'security', action: 'write' }
);

/**
 * 用户管理权限装饰器
 */
export const UserManagement = () => RequirePermissions(
  { resource: 'users', action: 'manage' }
);

/**
 * 策略管理权限装饰器
 */
export const PolicyManagement = () => RequirePermissions(
  { resource: 'policies', action: 'manage' }
);

/**
 * 威胁检测权限装饰器
 */
export const ThreatDetection = () => RequirePermissions(
  { resource: 'threats', action: 'detect' }
);

/**
 * 审计日志权限装饰器
 */
export const AuditLogs = () => RequirePermissions(
  { resource: 'audit', action: 'read' }
);
