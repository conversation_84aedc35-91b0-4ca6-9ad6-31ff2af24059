/**
 * 简化版安全防护体系服务启动文件
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('SecurityService');
  
  try {
    console.log('开始创建应用...');
    
    // 创建应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn'],
    });

    console.log('应用创建成功，开始配置...');

    // 获取配置服务
    const configService = app.get(ConfigService);

    // 设置全局前缀
    const globalPrefix = 'api/v1';
    app.setGlobalPrefix(globalPrefix);

    // 启用全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }));

    // 启用CORS
    app.enableCors({
      origin: '*',
      credentials: true,
    });

    console.log('基础配置完成，配置Swagger...');

    // 配置Swagger文档
    const swaggerConfig = new DocumentBuilder()
      .setTitle('安全防护体系服务API')
      .setDescription('DL引擎安全防护体系服务API文档')
      .setVersion('1.0.0')
      .addTag('security', '安全管理')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup(`${globalPrefix}/docs`, app, document);

    console.log('Swagger配置完成，启动服务...');

    // 启动HTTP服务
    const port = 5555; // 固定端口用于测试
    const host = '127.0.0.1';

    await app.listen(port, host);

    logger.log(`🚀 安全防护体系服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`🌐 全局前缀: /${globalPrefix}`);
    logger.log(`📚 API文档: http://${host}:${port}/${globalPrefix}/docs`);
    logger.log(`📊 健康检查: http://${host}:${port}/${globalPrefix}/health`);

  } catch (error) {
    logger.error('服务启动失败:', error);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

bootstrap();
