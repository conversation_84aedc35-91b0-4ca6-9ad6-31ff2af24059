/**
 * 测试启动文件
 */

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { TestAppModule } from './test-app.module';

async function bootstrap() {
  const logger = new Logger('TestApp');
  
  try {
    console.log('开始创建测试应用...');
    
    // 创建应用实例
    const app = await NestFactory.create(TestAppModule, {
      logger: ['log', 'error', 'warn'],
    });

    console.log('测试应用创建成功');

    // 启动HTTP服务
    const port = 5556;
    const host = '127.0.0.1';

    await app.listen(port, host);

    logger.log(`🚀 测试应用已启动: http://${host}:${port}`);

  } catch (error) {
    logger.error('测试应用启动失败:', error);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

bootstrap();
