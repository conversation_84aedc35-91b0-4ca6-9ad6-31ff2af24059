"use strict";
/**
 * 安全策略实体
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityPolicy = exports.PolicyStatus = exports.EnforcementMode = exports.PolicyType = void 0;
const typeorm_1 = require("typeorm");
var PolicyType;
(function (PolicyType) {
    PolicyType["ACCESS_CONTROL"] = "access_control";
    PolicyType["DATA_PROTECTION"] = "data_protection";
    PolicyType["NETWORK_SECURITY"] = "network_security";
    PolicyType["DEVICE_COMPLIANCE"] = "device_compliance";
    PolicyType["ZERO_TRUST"] = "zero_trust";
})(PolicyType || (exports.PolicyType = PolicyType = {}));
var EnforcementMode;
(function (EnforcementMode) {
    EnforcementMode["MONITOR"] = "monitor";
    EnforcementMode["ENFORCE"] = "enforce";
    EnforcementMode["BLOCK"] = "block";
})(EnforcementMode || (exports.EnforcementMode = EnforcementMode = {}));
var PolicyStatus;
(function (PolicyStatus) {
    PolicyStatus["ACTIVE"] = "active";
    PolicyStatus["INACTIVE"] = "inactive";
    PolicyStatus["DRAFT"] = "draft";
    PolicyStatus["ARCHIVED"] = "archived";
})(PolicyStatus || (exports.PolicyStatus = PolicyStatus = {}));
let SecurityPolicy = class SecurityPolicy {
};
exports.SecurityPolicy = SecurityPolicy;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "policyId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PolicyType,
        default: PolicyType.ACCESS_CONTROL,
    }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "policyType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: EnforcementMode,
        default: EnforcementMode.MONITOR,
    }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "enforcementMode", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PolicyStatus,
        default: PolicyStatus.DRAFT,
    }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 5 }),
    __metadata("design:type", Number)
], SecurityPolicy.prototype, "priority", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityPolicy.prototype, "scope", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Array)
], SecurityPolicy.prototype, "rules", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityPolicy.prototype, "conditions", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityPolicy.prototype, "actions", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], SecurityPolicy.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityPolicy.prototype, "effectiveFrom", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityPolicy.prototype, "effectiveTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], SecurityPolicy.prototype, "violationCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], SecurityPolicy.prototype, "lastViolation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], SecurityPolicy.prototype, "enabled", void 0);
__decorate([
    (0, typeorm_1.Column)('json', { nullable: true }),
    __metadata("design:type", Object)
], SecurityPolicy.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SecurityPolicy.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], SecurityPolicy.prototype, "updatedAt", void 0);
exports.SecurityPolicy = SecurityPolicy = __decorate([
    (0, typeorm_1.Entity)('security_policies'),
    (0, typeorm_1.Index)(['policyType', 'status']),
    (0, typeorm_1.Index)(['createdBy']),
    (0, typeorm_1.Index)(['priority'])
], SecurityPolicy);
