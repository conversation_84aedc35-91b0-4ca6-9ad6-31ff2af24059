/**
 * 数据库集成应用模块
 * 集成数据库支持的完整安全服务
 */

import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TerminusModule } from '@nestjs/terminus';

// 数据库模块
import { DatabaseModule } from './database/database.module';
import { DatabaseService } from './database/database.service';
import { DatabaseHealthIndicator } from './database/database-health.indicator';

// 安全模块
import { SecurityPolicyRepository } from './security/repositories/security-policy.repository';
import { SecurityEventRepository } from './security/repositories/security-event.repository';

// 控制器
import { SimpleController } from './simple-controller';
import { EnhancedSecurityController } from './enhanced-security.controller';

// 服务
import { EnhancedSecurityService } from './enhanced-security.service';

// 健康检查
import { HealthController } from './health/health.controller';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 数据库模块
    DatabaseModule,

    // JWT模块
    JwtModule.register({
      secret: 'security-service-jwt-secret-key-change-in-production',
      signOptions: {
        expiresIn: '24h',
      },
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 健康检查模块
    TerminusModule,
  ],
  controllers: [
    SimpleController,
    EnhancedSecurityController,
    HealthController,
  ],
  providers: [
    // 数据库服务
    DatabaseService,
    DatabaseHealthIndicator,
    
    // 仓库服务
    SecurityPolicyRepository,
    SecurityEventRepository,
    
    // 业务服务
    EnhancedSecurityService,
  ],
  exports: [
    DatabaseService,
    EnhancedSecurityService,
    SecurityPolicyRepository,
    SecurityEventRepository,
  ],
})
export class DatabaseIntegratedAppModule {
  constructor() {
    console.log('🔐 数据库集成安全防护体系服务模块已加载');
    console.log('✅ 数据库支持已集成');
    console.log('✅ 数据持久化功能已启用');
    console.log('✅ 安全事件存储已配置');
    console.log('✅ 策略管理已集成');
  }
}
