"use strict";
/**
 * 增强版应用模块
 * 集成核心安全功能
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedAppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
// 控制器
const simple_controller_1 = require("./simple-controller");
const enhanced_security_controller_1 = require("./enhanced-security.controller");
// 服务
const enhanced_security_service_1 = require("./enhanced-security.service");
let EnhancedAppModule = class EnhancedAppModule {
    constructor() {
        console.log('🔐 增强版安全防护体系服务模块已加载');
        console.log('✅ 核心安全功能已集成');
    }
};
exports.EnhancedAppModule = EnhancedAppModule;
exports.EnhancedAppModule = EnhancedAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            // 配置模块
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
                cache: true,
            }),
            // JWT模块
            jwt_1.JwtModule.register({
                secret: 'security-service-jwt-secret-key-change-in-production',
                signOptions: {
                    expiresIn: '24h',
                },
            }),
            // Passport模块
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
        ],
        controllers: [
            simple_controller_1.SimpleController,
            enhanced_security_controller_1.EnhancedSecurityController,
        ],
        providers: [
            enhanced_security_service_1.EnhancedSecurityService,
        ],
        exports: [
            enhanced_security_service_1.EnhancedSecurityService,
        ],
    }),
    __metadata("design:paramtypes", [])
], EnhancedAppModule);
