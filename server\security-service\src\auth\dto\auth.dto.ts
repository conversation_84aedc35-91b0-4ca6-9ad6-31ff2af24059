/**
 * 认证相关DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, <PERSON>Length, MaxLength, IsEnum, IsBoolean, IsArray } from 'class-validator';
import { UserRole } from '../entities/user.entity';

export class LoginDto {
  @ApiProperty({ description: '用户名或邮箱', example: 'admin' })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(6)
  @MaxLength(100)
  password: string;

  @ApiProperty({ description: '设备ID', example: 'device_12345', required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: '记住登录', example: false, required: false })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean;

  @ApiProperty({ description: 'MFA验证码', example: '123456', required: false })
  @IsOptional()
  @IsString()
  mfaCode?: string;
}

export class RegisterDto {
  @ApiProperty({ description: '用户名', example: 'newuser' })
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: '密码', example: 'password123' })
  @IsString()
  @MinLength(8)
  @MaxLength(100)
  password: string;

  @ApiProperty({ description: '确认密码', example: 'password123' })
  @IsString()
  confirmPassword: string;

  @ApiProperty({ description: '用户角色', enum: UserRole, example: UserRole.VIEWER, required: false })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @ApiProperty({ description: '名字', example: 'John', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: '姓氏', example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: '部门', example: 'Security', required: false })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiProperty({ description: '职位', example: 'Security Analyst', required: false })
  @IsOptional()
  @IsString()
  position?: string;
}

export class ChangePasswordDto {
  @ApiProperty({ description: '当前密码', example: 'oldpassword' })
  @IsString()
  currentPassword: string;

  @ApiProperty({ description: '新密码', example: 'newpassword123' })
  @IsString()
  @MinLength(8)
  @MaxLength(100)
  newPassword: string;

  @ApiProperty({ description: '确认新密码', example: 'newpassword123' })
  @IsString()
  confirmNewPassword: string;
}

export class ResetPasswordDto {
  @ApiProperty({ description: '重置令牌', example: 'reset_token_12345' })
  @IsString()
  token: string;

  @ApiProperty({ description: '新密码', example: 'newpassword123' })
  @IsString()
  @MinLength(8)
  @MaxLength(100)
  newPassword: string;

  @ApiProperty({ description: '确认新密码', example: 'newpassword123' })
  @IsString()
  confirmNewPassword: string;
}

export class ForgotPasswordDto {
  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  email: string;
}

export class RefreshTokenDto {
  @ApiProperty({ description: '刷新令牌', example: 'refresh_token_12345' })
  @IsString()
  refreshToken: string;
}

export class UpdateProfileDto {
  @ApiProperty({ description: '名字', example: 'John', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: '姓氏', example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ description: '电话', example: '+1234567890', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: '部门', example: 'Security', required: false })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiProperty({ description: '职位', example: 'Security Analyst', required: false })
  @IsOptional()
  @IsString()
  position?: string;

  @ApiProperty({ description: '时区', example: 'Asia/Shanghai', required: false })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiProperty({ description: '语言', example: 'zh-CN', required: false })
  @IsOptional()
  @IsString()
  language?: string;
}

export class UpdateSecuritySettingsDto {
  @ApiProperty({ description: '启用MFA', example: true, required: false })
  @IsOptional()
  @IsBoolean()
  mfaEnabled?: boolean;

  @ApiProperty({ description: 'MFA方法', example: 'totp', required: false })
  @IsOptional()
  @IsString()
  mfaMethod?: 'totp' | 'sms' | 'email';

  @ApiProperty({ description: 'IP白名单', example: ['***********', '********'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  ipWhitelist?: string[];
}

export class LoginResponseDto {
  @ApiProperty({ description: '访问令牌' })
  accessToken: string;

  @ApiProperty({ description: '刷新令牌' })
  refreshToken: string;

  @ApiProperty({ description: '令牌类型', example: 'Bearer' })
  tokenType: string;

  @ApiProperty({ description: '过期时间（秒）', example: 3600 })
  expiresIn: number;

  @ApiProperty({ description: '用户信息' })
  user: any;

  @ApiProperty({ description: '权限列表' })
  permissions: string[];

  @ApiProperty({ description: '是否需要MFA验证', required: false })
  requiresMfa?: boolean;

  @ApiProperty({ description: 'MFA挑战令牌', required: false })
  mfaChallengeToken?: string;
}

export class UserResponseDto {
  @ApiProperty({ description: '用户ID' })
  id: string;

  @ApiProperty({ description: '用户名' })
  username: string;

  @ApiProperty({ description: '邮箱' })
  email: string;

  @ApiProperty({ description: '角色', enum: UserRole })
  role: UserRole;

  @ApiProperty({ description: '状态' })
  status: string;

  @ApiProperty({ description: '个人资料' })
  profile: any;

  @ApiProperty({ description: '最后登录时间' })
  lastLoginAt?: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  @ApiProperty({ description: '是否启用MFA' })
  mfaEnabled: boolean;

  @ApiProperty({ description: '是否被锁定' })
  isLocked: boolean;

  @ApiProperty({ description: '密码是否过期' })
  isPasswordExpired: boolean;
}
