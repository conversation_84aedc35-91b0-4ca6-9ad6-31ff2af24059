"use strict";
/**
 * 用户实体
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = exports.UserStatus = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["SECURITY_ANALYST"] = "security_analyst";
    UserRole["OPERATOR"] = "operator";
    UserRole["VIEWER"] = "viewer";
    UserRole["GUEST"] = "guest";
})(UserRole || (exports.UserRole = UserRole = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["LOCKED"] = "locked";
    UserStatus["PENDING"] = "pending";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
class User {
    constructor(data) {
        Object.assign(this, data);
        this.id = data.id || `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.createdAt = data.createdAt || new Date();
        this.updatedAt = data.updatedAt || new Date();
        this.status = data.status || UserStatus.PENDING;
        this.permissions = data.permissions || [];
        this.profile = data.profile || {};
        this.securitySettings = data.securitySettings || {
            mfaEnabled: false,
            loginAttempts: 0,
            trustedDevices: [],
        };
    }
    /**
     * 检查用户是否有特定权限
     */
    hasPermission(resource, action) {
        // 管理员拥有所有权限
        if (this.role === UserRole.ADMIN) {
            return true;
        }
        // 检查具体权限
        return this.permissions.some(permission => permission.resource === resource &&
            permission.actions.includes(action));
    }
    /**
     * 检查用户是否可以访问
     */
    canAccess() {
        return this.status === UserStatus.ACTIVE &&
            (!this.securitySettings.lockedUntil || this.securitySettings.lockedUntil < new Date());
    }
    /**
     * 获取用户显示名称
     */
    getDisplayName() {
        if (this.profile.firstName && this.profile.lastName) {
            return `${this.profile.firstName} ${this.profile.lastName}`;
        }
        return this.username;
    }
    /**
     * 检查密码是否过期
     */
    isPasswordExpired() {
        if (!this.securitySettings.passwordExpiresAt) {
            return false;
        }
        return this.securitySettings.passwordExpiresAt < new Date();
    }
    /**
     * 检查账户是否被锁定
     */
    isLocked() {
        return this.securitySettings.lockedUntil && this.securitySettings.lockedUntil > new Date();
    }
    /**
     * 增加登录失败次数
     */
    incrementLoginAttempts() {
        this.securitySettings.loginAttempts++;
        // 如果失败次数超过5次，锁定账户1小时
        if (this.securitySettings.loginAttempts >= 5) {
            this.securitySettings.lockedUntil = new Date(Date.now() + 60 * 60 * 1000);
        }
        this.updatedAt = new Date();
    }
    /**
     * 重置登录失败次数
     */
    resetLoginAttempts() {
        this.securitySettings.loginAttempts = 0;
        this.securitySettings.lockedUntil = undefined;
        this.updatedAt = new Date();
    }
    /**
     * 更新最后登录信息
     */
    updateLastLogin(ip) {
        this.lastLoginAt = new Date();
        this.lastLoginIp = ip;
        this.resetLoginAttempts();
    }
    /**
     * 添加受信任设备
     */
    addTrustedDevice(deviceId) {
        if (!this.securitySettings.trustedDevices.includes(deviceId)) {
            this.securitySettings.trustedDevices.push(deviceId);
            this.updatedAt = new Date();
        }
    }
    /**
     * 移除受信任设备
     */
    removeTrustedDevice(deviceId) {
        const index = this.securitySettings.trustedDevices.indexOf(deviceId);
        if (index > -1) {
            this.securitySettings.trustedDevices.splice(index, 1);
            this.updatedAt = new Date();
        }
    }
    /**
     * 检查设备是否受信任
     */
    isTrustedDevice(deviceId) {
        return this.securitySettings.trustedDevices.includes(deviceId);
    }
    /**
     * 转换为安全的公开对象（不包含敏感信息）
     */
    toSafeObject() {
        return {
            id: this.id,
            username: this.username,
            email: this.email,
            role: this.role,
            status: this.status,
            profile: this.profile,
            lastLoginAt: this.lastLoginAt,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
            mfaEnabled: this.securitySettings.mfaEnabled,
            isLocked: this.isLocked(),
            isPasswordExpired: this.isPasswordExpired(),
        };
    }
}
exports.User = User;
