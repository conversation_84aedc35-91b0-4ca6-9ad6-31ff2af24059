/**
 * 安全控制器
 * 处理安全相关的HTTP请求
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { SecurityService } from './security.service';
import { ZeroTrustSecurityService } from './zero-trust-security.service';
import { ThreatDetectionService } from './threat-detection.service';
import { SecurityPolicyService } from './security-policy.service';
import { RiskAssessmentService } from './risk-assessment.service';

import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { SecurityGuard } from './guards/security.guard';
import { RateLimitGuard } from './guards/rate-limit.guard';

import { CreateSecurityPolicyDto } from './dto/create-security-policy.dto';
import { UpdateSecurityPolicyDto } from './dto/update-security-policy.dto';
import { AuthenticationRequestDto } from './dto/authentication-request.dto';
import { ThreatDetectionRequestDto } from './dto/threat-detection-request.dto';
import { RiskAssessmentRequestDto } from './dto/risk-assessment-request.dto';

@ApiTags('security')
@Controller('security')
@UseGuards(JwtAuthGuard, SecurityGuard, RateLimitGuard)
@ApiBearerAuth()
export class SecurityController {
  private readonly logger = new Logger(SecurityController.name);

  constructor(
    private readonly securityService: SecurityService,
    private readonly zeroTrustService: ZeroTrustSecurityService,
    private readonly threatDetectionService: ThreatDetectionService,
    private readonly policyService: SecurityPolicyService,
    private readonly riskAssessmentService: RiskAssessmentService,
  ) {}

  // ==================== 零信任安全 ====================

  @Post('zero-trust/authenticate')
  @ApiOperation({ summary: '零信任身份验证' })
  @ApiResponse({ status: 200, description: '认证成功' })
  @ApiResponse({ status: 401, description: '认证失败' })
  async authenticateZeroTrust(@Body() authRequest: AuthenticationRequestDto) {
    try {
      this.logger.log(`零信任认证请求: ${authRequest.userId}`);
      const result = await this.zeroTrustService.authenticateAndAuthorize(authRequest);
      return {
        success: true,
        data: result,
        message: '零信任认证完成',
      };
    } catch (error) {
      this.logger.error('零信任认证失败', error);
      throw new HttpException(
        {
          success: false,
          message: '零信任认证失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }
  }

  @Get('zero-trust/policies')
  @ApiOperation({ summary: '获取零信任策略列表' })
  @ApiResponse({ status: 200, description: '策略列表' })
  async getZeroTrustPolicies() {
    try {
      const policies = await this.policyService.getAllPolicies();
      return {
        success: true,
        data: policies,
        message: '获取策略列表成功',
      };
    } catch (error) {
      this.logger.error('获取策略列表失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取策略列表失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('zero-trust/policies')
  @ApiOperation({ summary: '创建零信任策略' })
  @ApiResponse({ status: 201, description: '策略创建成功' })
  async createZeroTrustPolicy(@Body() policyDto: CreateSecurityPolicyDto) {
    try {
      this.logger.log(`创建零信任策略: ${policyDto.name}`);
      const policyId = await this.zeroTrustService.createZeroTrustPolicy(policyDto as any);
      return {
        success: true,
        data: { policyId },
        message: '零信任策略创建成功',
      };
    } catch (error) {
      this.logger.error('创建零信任策略失败', error);
      throw new HttpException(
        {
          success: false,
          message: '创建零信任策略失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  // ==================== 威胁检测 ====================

  @Post('threat-detection/scan')
  @ApiOperation({ summary: '执行威胁检测扫描' })
  @ApiResponse({ status: 200, description: '扫描完成' })
  async performThreatDetection(@Body() scanRequest: ThreatDetectionRequestDto) {
    try {
      this.logger.log(`威胁检测扫描: ${scanRequest.target}`);
      const result = await this.threatDetectionService.performThreatScan(scanRequest);
      return {
        success: true,
        data: result,
        message: '威胁检测扫描完成',
      };
    } catch (error) {
      this.logger.error('威胁检测扫描失败', error);
      throw new HttpException(
        {
          success: false,
          message: '威胁检测扫描失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('threat-detection/events')
  @ApiOperation({ summary: '获取安全事件列表' })
  @ApiQuery({ name: 'page', required: false, description: '页码' })
  @ApiQuery({ name: 'limit', required: false, description: '每页数量' })
  @ApiQuery({ name: 'severity', required: false, description: '严重程度' })
  async getSecurityEvents(
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('severity') severity?: string,
  ) {
    try {
      const events = await this.threatDetectionService.getSecurityEvents({
        page,
        limit,
        severity,
      });
      return {
        success: true,
        data: events,
        message: '获取安全事件列表成功',
      };
    } catch (error) {
      this.logger.error('获取安全事件列表失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取安全事件列表失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== 风险评估 ====================

  @Post('risk-assessment')
  @ApiOperation({ summary: '执行风险评估' })
  @ApiResponse({ status: 200, description: '风险评估完成' })
  async performRiskAssessment(@Body() assessmentRequest: RiskAssessmentRequestDto) {
    try {
      this.logger.log(`风险评估: ${assessmentRequest.target}`);
      const result = await this.riskAssessmentService.performAssessment(assessmentRequest);
      return {
        success: true,
        data: result,
        message: '风险评估完成',
      };
    } catch (error) {
      this.logger.error('风险评估失败', error);
      throw new HttpException(
        {
          success: false,
          message: '风险评估失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // ==================== 安全状态 ====================

  @Get('status')
  @ApiOperation({ summary: '获取安全状态' })
  @ApiResponse({ status: 200, description: '安全状态信息' })
  async getSecurityStatus() {
    try {
      const status = await this.zeroTrustService.getSecurityStatus();
      return {
        success: true,
        data: status,
        message: '获取安全状态成功',
      };
    } catch (error) {
      this.logger.error('获取安全状态失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取安全状态失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('metrics')
  @ApiOperation({ summary: '获取安全指标' })
  @ApiResponse({ status: 200, description: '安全指标信息' })
  async getSecurityMetrics() {
    try {
      const metrics = await this.securityService.getSecurityMetrics();
      return {
        success: true,
        data: metrics,
        message: '获取安全指标成功',
      };
    } catch (error) {
      this.logger.error('获取安全指标失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取安全指标失败',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
