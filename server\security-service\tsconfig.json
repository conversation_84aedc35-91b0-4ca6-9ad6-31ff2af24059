{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "resolveJsonModule": true, "esModuleInterop": true, "strict": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "paths": {"@/*": ["src/*"], "@/security/*": ["src/security/*"], "@/auth/*": ["src/auth/*"], "@/encryption/*": ["src/encryption/*"], "@/blockchain/*": ["src/blockchain/*"], "@/privacy/*": ["src/privacy/*"], "@/monitoring/*": ["src/monitoring/*"], "@/compliance/*": ["src/compliance/*"], "@/common/*": ["src/common/*"], "@/health/*": ["src/health/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.spec.ts", "**/*.test.ts"]}