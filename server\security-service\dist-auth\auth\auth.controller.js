"use strict";
/**
 * 认证控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AuthController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const auth_dto_1 = require("./dto/auth.dto");
const auth_decorators_1 = require("./decorators/auth.decorators");
let AuthController = AuthController_1 = class AuthController {
    constructor(authService) {
        this.authService = authService;
        this.logger = new common_1.Logger(AuthController_1.name);
    }
    // ==================== 公开端点 ====================
    async login(loginDto, req) {
        try {
            const clientIp = this.getClientIp(req);
            const userAgent = req.headers['user-agent'] || 'Unknown';
            this.logger.log(`登录尝试: ${loginDto.username} from ${clientIp}`);
            const result = await this.authService.login(loginDto, clientIp, userAgent);
            return {
                success: true,
                data: result,
                message: result.requiresMfa ? '需要MFA验证' : '登录成功',
            };
        }
        catch (error) {
            this.logger.error('登录失败', error);
            throw new common_1.HttpException({
                success: false,
                message: '登录失败',
                error: error instanceof Error ? error.message : 'Unknown error',
            }, common_1.HttpStatus.UNAUTHORIZED);
        }
    }
    async register(registerDto) {
        try {
            this.logger.log(`注册尝试: ${registerDto.username}`);
            const user = await this.authService.register(registerDto);
            return {
                success: true,
                data: user.toSafeObject(),
                message: '注册成功，等待管理员激活',
            };
        }
        catch (error) {
            this.logger.error('注册失败', error);
            throw new common_1.HttpException({
                success: false,
                message: '注册失败',
                error: error instanceof Error ? error.message : 'Unknown error',
            }, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    // ==================== 认证端点 ====================
    async logout(sessionId) {
        try {
            await this.authService.logout(sessionId);
            return {
                success: true,
                message: '登出成功',
            };
        }
        catch (error) {
            this.logger.error('登出失败', error);
            throw new common_1.HttpException({
                success: false,
                message: '登出失败',
                error: error instanceof Error ? error.message : 'Unknown error',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProfile(userId) {
        try {
            const user = await this.authService.getUserById(userId);
            if (!user) {
                throw new common_1.HttpException('用户不存在', common_1.HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: user.toSafeObject(),
                message: '获取用户资料成功',
            };
        }
        catch (error) {
            this.logger.error('获取用户资料失败', error);
            throw new common_1.HttpException({
                success: false,
                message: '获取用户资料失败',
                error: error instanceof Error ? error.message : 'Unknown error',
            }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    // ==================== 辅助方法 ====================
    getClientIp(req) {
        return req.headers['x-forwarded-for'] ||
            req.headers['x-real-ip'] ||
            req.connection?.remoteAddress ||
            req.socket?.remoteAddress ||
            req.ip ||
            '127.0.0.1';
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, auth_decorators_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '用户登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功', type: auth_dto_1.LoginResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '认证失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.LoginDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, auth_decorators_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '用户注册' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '注册成功', type: auth_dto_1.UserResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '注册失败' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '用户登出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登出成功' }),
    __param(0, (0, auth_decorators_1.CurrentUser)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户资料' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '用户资料', type: auth_dto_1.UserResponseDto }),
    __param(0, (0, auth_decorators_1.UserId)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
exports.AuthController = AuthController = AuthController_1 = __decorate([
    (0, swagger_1.ApiTags)('auth'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
