"use strict";
/**
 * 认证相关装饰器
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditLogs = exports.ThreatDetection = exports.PolicyManagement = exports.UserManagement = exports.SecurityWrite = exports.SecurityRead = exports.SecurityManagement = exports.ViewerOrAbove = exports.OperatorOrAbove = exports.SecurityAnalystOrAbove = exports.AdminOnly = exports.SessionId = exports.DeviceId = exports.ClientIp = exports.UserRole = exports.UserId = exports.CurrentUser = exports.Public = exports.RequirePermissions = exports.Roles = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../entities/user.entity");
const roles_guard_1 = require("../guards/roles.guard");
const permissions_guard_1 = require("../guards/permissions.guard");
/**
 * 角色装饰器
 */
const Roles = (...roles) => (0, common_1.SetMetadata)(roles_guard_1.ROLES_KEY, roles);
exports.Roles = Roles;
/**
 * 权限装饰器
 */
const RequirePermissions = (...permissions) => (0, common_1.SetMetadata)(permissions_guard_1.PERMISSIONS_KEY, permissions);
exports.RequirePermissions = RequirePermissions;
/**
 * 公开路由装饰器（不需要认证）
 */
const Public = () => (0, common_1.SetMetadata)('isPublic', true);
exports.Public = Public;
/**
 * 获取当前用户装饰器
 */
exports.CurrentUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    return data ? user?.[data] : user;
});
/**
 * 获取用户ID装饰器
 */
exports.UserId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.userId;
});
/**
 * 获取用户角色装饰器
 */
exports.UserRole = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.role;
});
/**
 * 获取客户端IP装饰器
 */
exports.ClientIp = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.clientIp ||
        request.headers['x-forwarded-for'] ||
        request.headers['x-real-ip'] ||
        request.connection?.remoteAddress ||
        request.socket?.remoteAddress ||
        request.ip ||
        '127.0.0.1';
});
/**
 * 获取设备ID装饰器
 */
exports.DeviceId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.deviceId;
});
/**
 * 获取会话ID装饰器
 */
exports.SessionId = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user?.sessionId;
});
/**
 * 管理员权限装饰器
 */
const AdminOnly = () => (0, exports.Roles)(user_entity_1.UserRole.ADMIN);
exports.AdminOnly = AdminOnly;
/**
 * 安全分析师权限装饰器
 */
const SecurityAnalystOrAbove = () => (0, exports.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SECURITY_ANALYST);
exports.SecurityAnalystOrAbove = SecurityAnalystOrAbove;
/**
 * 操作员权限装饰器
 */
const OperatorOrAbove = () => (0, exports.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SECURITY_ANALYST, user_entity_1.UserRole.OPERATOR);
exports.OperatorOrAbove = OperatorOrAbove;
/**
 * 查看者权限装饰器
 */
const ViewerOrAbove = () => (0, exports.Roles)(user_entity_1.UserRole.ADMIN, user_entity_1.UserRole.SECURITY_ANALYST, user_entity_1.UserRole.OPERATOR, user_entity_1.UserRole.VIEWER);
exports.ViewerOrAbove = ViewerOrAbove;
/**
 * 安全管理权限装饰器
 */
const SecurityManagement = () => (0, exports.RequirePermissions)({ resource: 'security', action: 'manage' });
exports.SecurityManagement = SecurityManagement;
/**
 * 安全读取权限装饰器
 */
const SecurityRead = () => (0, exports.RequirePermissions)({ resource: 'security', action: 'read' });
exports.SecurityRead = SecurityRead;
/**
 * 安全写入权限装饰器
 */
const SecurityWrite = () => (0, exports.RequirePermissions)({ resource: 'security', action: 'write' });
exports.SecurityWrite = SecurityWrite;
/**
 * 用户管理权限装饰器
 */
const UserManagement = () => (0, exports.RequirePermissions)({ resource: 'users', action: 'manage' });
exports.UserManagement = UserManagement;
/**
 * 策略管理权限装饰器
 */
const PolicyManagement = () => (0, exports.RequirePermissions)({ resource: 'policies', action: 'manage' });
exports.PolicyManagement = PolicyManagement;
/**
 * 威胁检测权限装饰器
 */
const ThreatDetection = () => (0, exports.RequirePermissions)({ resource: 'threats', action: 'detect' });
exports.ThreatDetection = ThreatDetection;
/**
 * 审计日志权限装饰器
 */
const AuditLogs = () => (0, exports.RequirePermissions)({ resource: 'audit', action: 'read' });
exports.AuditLogs = AuditLogs;
