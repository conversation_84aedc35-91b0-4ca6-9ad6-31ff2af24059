/**
 * 安全事件实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum EventSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum EventStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  FALSE_POSITIVE = 'false_positive',
  CLOSED = 'closed',
}

export enum EventType {
  AUTHENTICATION_FAILURE = 'authentication_failure',
  AUTHORIZATION_VIOLATION = 'authorization_violation',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  MALWARE_DETECTION = 'malware_detection',
  INTRUSION_ATTEMPT = 'intrusion_attempt',
  DATA_BREACH = 'data_breach',
  POLICY_VIOLATION = 'policy_violation',
  SYSTEM_COMPROMISE = 'system_compromise',
  NETWORK_ANOMALY = 'network_anomaly',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
}

@Entity('security_events')
@Index(['eventType', 'severity'])
@Index(['status'])
@Index(['timestamp'])
@Index(['userId'])
@Index(['ipAddress'])
export class SecurityEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  eventId: string;

  @Column({
    type: 'enum',
    enum: EventType,
  })
  eventType: EventType;

  @Column({
    type: 'enum',
    enum: EventSeverity,
    default: EventSeverity.LOW,
  })
  severity: EventSeverity;

  @Column({
    type: 'enum',
    enum: EventStatus,
    default: EventStatus.OPEN,
  })
  status: EventStatus;

  @Column()
  source: string;

  @Column({ nullable: true })
  target: string;

  @Column('text')
  description: string;

  @Column({ nullable: true })
  userId: string;

  @Column({ nullable: true })
  sessionId: string;

  @Column({ nullable: true })
  deviceId: string;

  @Column({ nullable: true })
  ipAddress: string;

  @Column('text', { nullable: true })
  userAgent: string;

  @Column({ nullable: true })
  location: string;

  @Column('json', { nullable: true })
  evidence: any;

  @Column('json', { nullable: true })
  metadata: any;

  @Column('json', { nullable: true })
  riskFactors: {
    type: string;
    severity: EventSeverity;
    description: string;
    score: number;
    evidence: any;
  }[];

  @Column({ type: 'int', default: 0 })
  riskScore: number;

  @Column({ type: 'int', default: 100 })
  trustScore: number;

  @Column('json', { nullable: true })
  response: {
    actions: string[];
    automated: boolean;
    responseTime: number;
    effectiveness: number;
    cost: number;
  };

  @Column({ nullable: true })
  assignedTo: string;

  @Column({ nullable: true })
  resolvedBy: string;

  @Column({ type: 'datetime', nullable: true })
  resolvedAt: Date;

  @Column('text', { nullable: true })
  resolution: string;

  @Column({ type: 'datetime' })
  timestamp: Date;

  @Column({ type: 'datetime', nullable: true })
  detectedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  acknowledgedAt: Date;

  @Column({ type: 'datetime', nullable: true })
  escalatedAt: Date;

  @Column('json', { nullable: true })
  tags: string[];

  @Column({ type: 'boolean', default: false })
  falsePositive: boolean;

  @Column('text', { nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
