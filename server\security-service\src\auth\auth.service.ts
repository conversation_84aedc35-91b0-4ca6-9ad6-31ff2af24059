/**
 * 认证服务
 */

import { Injectable, Logger, UnauthorizedException, BadRequestException, ConflictException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { User, UserRole, UserStatus } from './entities/user.entity';
import { MemoryStorageService } from '../memory-storage.service';
import { LoginDto, RegisterDto, ChangePasswordDto, LoginResponseDto } from './dto/auth.dto';
import { JwtPayload } from './strategies/jwt.strategy';

export interface UserSession {
  id: string;
  userId: string;
  deviceId?: string;
  ip: string;
  userAgent: string;
  createdAt: Date;
  lastAccessAt: Date;
  expiresAt: Date;
  isActive: boolean;
}

export interface AccessLog {
  id: string;
  userId: string;
  ip: string;
  userAgent: string;
  endpoint: string;
  method: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly users: Map<string, User> = new Map();
  private readonly sessions: Map<string, UserSession> = new Map();
  private readonly accessLogs: Map<string, AccessLog> = new Map();
  private readonly refreshTokens: Map<string, { userId: string; expiresAt: Date }> = new Map();

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private memoryStorageService: MemoryStorageService,
  ) {
    this.initializeDefaultUsers();
  }

  /**
   * 初始化默认用户
   */
  private async initializeDefaultUsers(): Promise<void> {
    try {
      // 创建默认管理员用户
      const adminExists = Array.from(this.users.values()).some(user => user.role === UserRole.ADMIN);

      if (!adminExists) {
        const adminUser = new User({
          username: 'admin',
          email: '<EMAIL>',
          passwordHash: await this.hashPassword('admin123'),
          salt: await this.generateSalt(),
          role: UserRole.ADMIN,
          status: UserStatus.ACTIVE,
          permissions: [
            { resource: '*', actions: ['*'] }, // 管理员拥有所有权限
          ],
          profile: {
            firstName: 'System',
            lastName: 'Administrator',
            department: 'Security',
            position: 'System Administrator',
          },
        });

        this.users.set(adminUser.id, adminUser);
        this.logger.log('默认管理员用户已创建: admin/admin123');
      }

      // 创建默认安全分析师用户
      const analystExists = Array.from(this.users.values()).some(user => user.username === 'analyst');

      if (!analystExists) {
        const analystUser = new User({
          username: 'analyst',
          email: '<EMAIL>',
          passwordHash: await this.hashPassword('analyst123'),
          salt: await this.generateSalt(),
          role: UserRole.SECURITY_ANALYST,
          status: UserStatus.ACTIVE,
          permissions: [
            { resource: 'security', actions: ['read', 'write', 'analyze'] },
            { resource: 'threats', actions: ['read', 'detect', 'respond'] },
            { resource: 'policies', actions: ['read', 'write'] },
            { resource: 'audit', actions: ['read'] },
          ],
          profile: {
            firstName: 'Security',
            lastName: 'Analyst',
            department: 'Security',
            position: 'Security Analyst',
          },
        });

        this.users.set(analystUser.id, analystUser);
        this.logger.log('默认安全分析师用户已创建: analyst/analyst123');
      }

      this.logger.log(`已初始化 ${this.users.size} 个用户`);
    } catch (error) {
      this.logger.error('初始化默认用户失败', error);
    }
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginDto, clientIp: string, userAgent: string): Promise<LoginResponseDto> {
    try {
      // 查找用户
      const user = this.findUserByUsernameOrEmail(loginDto.username);
      if (!user) {
        throw new UnauthorizedException('用户名或密码错误');
      }

      // 检查用户状态
      if (!user.canAccess()) {
        throw new UnauthorizedException('用户账户被禁用或锁定');
      }

      // 验证密码
      const isPasswordValid = await this.verifyPassword(loginDto.password, user.passwordHash);
      if (!isPasswordValid) {
        user.incrementLoginAttempts();
        await this.recordAccessLog(user.id, {
          ip: clientIp,
          userAgent,
          endpoint: '/auth/login',
          method: 'POST',
          timestamp: new Date(),
          success: false,
          errorMessage: '密码错误',
        });
        throw new UnauthorizedException('用户名或密码错误');
      }

      // 检查是否需要MFA
      if (user.securitySettings.mfaEnabled && !loginDto.mfaCode) {
        // 生成MFA挑战令牌
        const mfaChallengeToken = this.generateMfaChallengeToken(user.id);

        return {
          accessToken: '',
          refreshToken: '',
          tokenType: 'Bearer',
          expiresIn: 0,
          user: null,
          permissions: [],
          requiresMfa: true,
          mfaChallengeToken,
        };
      }

      // 验证MFA（如果提供了）
      if (user.securitySettings.mfaEnabled && loginDto.mfaCode) {
        const isMfaValid = await this.verifyMfaCode(user.id, loginDto.mfaCode);
        if (!isMfaValid) {
          throw new UnauthorizedException('MFA验证码错误');
        }
      }

      // 创建会话
      const session = await this.createSession(user.id, loginDto.deviceId, clientIp, userAgent);

      // 生成JWT令牌
      const tokens = await this.generateTokens(user, session.id, loginDto.deviceId);

      // 更新用户登录信息
      user.updateLastLogin(clientIp);

      // 记录成功登录日志
      await this.recordAccessLog(user.id, {
        ip: clientIp,
        userAgent,
        endpoint: '/auth/login',
        method: 'POST',
        timestamp: new Date(),
        success: true,
      });

      this.logger.log(`用户登录成功: ${user.username} (${clientIp})`);

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        tokenType: 'Bearer',
        expiresIn: 3600, // 1小时
        user: user.toSafeObject(),
        permissions: user.permissions.map(p => `${p.resource}:${p.actions.join(',')}`),
      };
    } catch (error) {
      this.logger.error('用户登录失败', error);
      throw error;
    }
  }

  /**
   * 用户注册
   */
  async register(registerDto: RegisterDto): Promise<User> {
    try {
      // 检查用户名是否已存在
      if (this.findUserByUsernameOrEmail(registerDto.username)) {
        throw new ConflictException('用户名已存在');
      }

      // 检查邮箱是否已存在
      if (this.findUserByUsernameOrEmail(registerDto.email)) {
        throw new ConflictException('邮箱已存在');
      }

      // 验证密码确认
      if (registerDto.password !== registerDto.confirmPassword) {
        throw new BadRequestException('密码确认不匹配');
      }

      // 创建新用户
      const user = new User({
        username: registerDto.username,
        email: registerDto.email,
        passwordHash: await this.hashPassword(registerDto.password),
        salt: await this.generateSalt(),
        role: registerDto.role || UserRole.VIEWER,
        status: UserStatus.PENDING, // 新用户需要激活
        permissions: this.getDefaultPermissions(registerDto.role || UserRole.VIEWER),
        profile: {
          firstName: registerDto.firstName,
          lastName: registerDto.lastName,
          department: registerDto.department,
          position: registerDto.position,
        },
      });

      this.users.set(user.id, user);
      this.logger.log(`新用户注册: ${user.username}`);

      return user;
    } catch (error) {
      this.logger.error('用户注册失败', error);
      throw error;
    }
  }

  /**
   * 验证用户ID
   */
  async validateUserById(userId: string): Promise<User | null> {
    return this.users.get(userId) || null;
  }

  /**
   * 验证会话
   */
  async validateSession(sessionId: string, userId: string): Promise<boolean> {
    const session = this.sessions.get(sessionId);
    return session &&
           session.userId === userId &&
           session.isActive &&
           session.expiresAt > new Date();
  }

  /**
   * 记录访问日志
   */
  async recordAccessLog(userId: string, logData: Partial<AccessLog>): Promise<void> {
    const log: AccessLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      ip: logData.ip || '127.0.0.1',
      userAgent: logData.userAgent || 'Unknown',
      endpoint: logData.endpoint || '',
      method: logData.method || 'GET',
      timestamp: logData.timestamp || new Date(),
      success: logData.success !== undefined ? logData.success : true,
      errorMessage: logData.errorMessage,
    };

    this.accessLogs.set(log.id, log);
  }

  /**
   * 用户登出
   */
  async logout(sessionId: string): Promise<void> {
    try {
      const session = this.sessions.get(sessionId);
      if (session) {
        session.isActive = false;
        this.logger.log(`用户登出: 会话 ${sessionId}`);
      }
    } catch (error) {
      this.logger.error('用户登出失败', error);
      throw error;
    }
  }

  /**
   * 获取所有用户
   */
  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values()).map(user => user);
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(userId: string): Promise<User | null> {
    return this.users.get(userId) || null;
  }

  /**
   * 更新用户状态
   */
  async updateUserStatus(userId: string, status: UserStatus): Promise<User | null> {
    const user = this.users.get(userId);
    if (user) {
      user.status = status;
      user.updatedAt = new Date();
      return user;
    }
    return null;
  }

  // ==================== 私有辅助方法 ====================

  private findUserByUsernameOrEmail(usernameOrEmail: string): User | null {
    return Array.from(this.users.values()).find(user =>
      user.username === usernameOrEmail || user.email === usernameOrEmail
    ) || null;
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  private async generateSalt(): Promise<string> {
    return crypto.randomBytes(32).toString('hex');
  }

  private async generateTokens(user: User, sessionId?: string, deviceId?: string): Promise<{ accessToken: string; refreshToken: string }> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      permissions: user.permissions.map(p => `${p.resource}:${p.actions.join(',')}`),
      sessionId,
      deviceId,
    };

    const accessToken = this.jwtService.sign(payload, { expiresIn: '1h' });
    const refreshToken = crypto.randomBytes(32).toString('hex');

    // 存储刷新令牌
    this.refreshTokens.set(refreshToken, {
      userId: user.id,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天
    });

    return { accessToken, refreshToken };
  }

  private async createSession(userId: string, deviceId: string, ip: string, userAgent: string): Promise<UserSession> {
    const session: UserSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      deviceId,
      ip,
      userAgent,
      createdAt: new Date(),
      lastAccessAt: new Date(),
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时
      isActive: true,
    };

    this.sessions.set(session.id, session);
    return session;
  }

  private generateMfaChallengeToken(userId: string): string {
    // 简化的MFA挑战令牌生成
    return `mfa_${userId}_${Date.now()}`;
  }

  private async verifyMfaCode(userId: string, code: string): Promise<boolean> {
    // 简化的MFA验证（实际应该验证TOTP、SMS等）
    return code === '123456'; // 演示用固定验证码
  }

  private getDefaultPermissions(role: UserRole): any[] {
    switch (role) {
      case UserRole.ADMIN:
        return [{ resource: '*', actions: ['*'] }];
      case UserRole.SECURITY_ANALYST:
        return [
          { resource: 'security', actions: ['read', 'write', 'analyze'] },
          { resource: 'threats', actions: ['read', 'detect', 'respond'] },
          { resource: 'policies', actions: ['read', 'write'] },
          { resource: 'audit', actions: ['read'] },
        ];
      case UserRole.OPERATOR:
        return [
          { resource: 'security', actions: ['read', 'write'] },
          { resource: 'threats', actions: ['read', 'respond'] },
          { resource: 'policies', actions: ['read'] },
        ];
      case UserRole.VIEWER:
        return [
          { resource: 'security', actions: ['read'] },
          { resource: 'threats', actions: ['read'] },
          { resource: 'policies', actions: ['read'] },
        ];
      default:
        return [{ resource: 'security', actions: ['read'] }];
    }
  }
}
