"use strict";
/**
 * 数据库模块
 * 管理数据库连接和配置
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
// 实体导入
const security_policy_entity_1 = require("../security/entities/security-policy.entity");
const security_event_entity_1 = require("../security/entities/security-event.entity");
const threat_intelligence_entity_1 = require("../security/entities/threat-intelligence.entity");
const security_audit_entity_1 = require("../security/entities/security-audit.entity");
const device_trust_entity_1 = require("../security/entities/device-trust.entity");
const user_session_entity_1 = require("../security/entities/user-session.entity");
const security_metrics_entity_1 = require("../security/entities/security-metrics.entity");
let DatabaseModule = class DatabaseModule {
    constructor(configService) {
        this.configService = configService;
        const dbEnabled = this.configService.get('DB_ENABLED', 'true') === 'true';
        if (dbEnabled) {
            console.log('📊 数据库模块已加载');
            console.log(`📍 数据库主机: ${this.configService.get('DB_HOST')}`);
            console.log(`📍 数据库端口: ${this.configService.get('DB_PORT')}`);
            console.log(`📍 数据库名称: ${this.configService.get('DB_DATABASE')}`);
        }
        else {
            console.log('📊 数据库模块已加载（内存模式）');
        }
    }
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const dbEnabled = configService.get('DB_ENABLED', 'true') === 'true';
                    if (!dbEnabled) {
                        console.log('📊 数据库连接已禁用，使用内存存储');
                        return {
                            type: 'sqlite',
                            database: ':memory:',
                            entities: [],
                            synchronize: false,
                            logging: false,
                        };
                    }
                    console.log('📊 正在配置数据库连接...');
                    return {
                        type: 'mysql',
                        host: configService.get('DB_HOST', 'localhost'),
                        port: configService.get('DB_PORT', 3306),
                        username: configService.get('DB_USERNAME', 'root'),
                        password: configService.get('DB_PASSWORD', ''),
                        database: configService.get('DB_DATABASE', 'security_service'),
                        entities: [
                            security_policy_entity_1.SecurityPolicy,
                            security_event_entity_1.SecurityEvent,
                            threat_intelligence_entity_1.ThreatIntelligence,
                            security_audit_entity_1.SecurityAudit,
                            device_trust_entity_1.DeviceTrust,
                            user_session_entity_1.UserSession,
                            security_metrics_entity_1.SecurityMetrics,
                        ],
                        synchronize: configService.get('DB_SYNCHRONIZE', false),
                        logging: configService.get('DB_LOGGING', false),
                        timezone: '+08:00',
                        charset: 'utf8mb4',
                        extra: {
                            connectionLimit: configService.get('DB_CONNECTION_LIMIT', 10),
                            acquireTimeout: configService.get('DB_ACQUIRE_TIMEOUT', 60000),
                            timeout: configService.get('DB_TIMEOUT', 60000),
                        },
                        retryAttempts: 3,
                        retryDelay: 3000,
                        autoLoadEntities: true,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            // 特性模块 - 只在数据库启用时加载
            typeorm_1.TypeOrmModule.forFeature([
                security_policy_entity_1.SecurityPolicy,
                security_event_entity_1.SecurityEvent,
                threat_intelligence_entity_1.ThreatIntelligence,
                security_audit_entity_1.SecurityAudit,
                device_trust_entity_1.DeviceTrust,
                user_session_entity_1.UserSession,
                security_metrics_entity_1.SecurityMetrics,
            ]),
        ],
        exports: [typeorm_1.TypeOrmModule],
    }),
    __metadata("design:paramtypes", [config_1.ConfigService])
], DatabaseModule);
