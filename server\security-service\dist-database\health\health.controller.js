"use strict";
/**
 * 健康检查控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const terminus_1 = require("@nestjs/terminus");
const database_health_indicator_1 = require("../database/database-health.indicator");
const public_decorator_1 = require("../common/decorators/public.decorator");
let HealthController = class HealthController {
    constructor(health, db, memory, disk, databaseHealth) {
        this.health = health;
        this.db = db;
        this.memory = memory;
        this.disk = disk;
        this.databaseHealth = databaseHealth;
    }
    check() {
        return this.health.check([
            // 自定义数据库健康检查
            () => this.databaseHealth.isHealthy('database'),
            // 内存使用检查
            () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
            () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
            // 磁盘使用检查
            () => this.disk.checkStorage('storage', {
                path: '/',
                thresholdPercent: 0.9,
            }),
        ]);
    }
    ready() {
        return this.health.check([
            () => this.databaseHealth.isHealthy('database'),
        ]);
    }
    live() {
        return this.health.check([
            () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024),
        ]);
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '健康检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务健康状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('ready'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '就绪检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务就绪状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "ready", null);
__decorate([
    (0, common_1.Get)('live'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '存活检查' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务存活状态' }),
    (0, terminus_1.HealthCheck)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "live", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        terminus_1.TypeOrmHealthIndicator,
        terminus_1.MemoryHealthIndicator,
        terminus_1.DiskHealthIndicator,
        database_health_indicator_1.DatabaseHealthIndicator])
], HealthController);
