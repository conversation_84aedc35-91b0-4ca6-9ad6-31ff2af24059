/**
 * 数据库模块
 * 管理数据库连接和配置
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

// 实体导入
import { SecurityPolicy } from '../security/entities/security-policy.entity';
import { SecurityEvent } from '../security/entities/security-event.entity';
import { ThreatIntelligence } from '../security/entities/threat-intelligence.entity';
import { SecurityAudit } from '../security/entities/security-audit.entity';
import { DeviceTrust } from '../security/entities/device-trust.entity';
import { UserSession } from '../security/entities/user-session.entity';
import { SecurityMetrics } from '../security/entities/security-metrics.entity';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const dbEnabled = configService.get<string>('DB_ENABLED', 'true') === 'true';

        if (!dbEnabled) {
          console.log('📊 数据库连接已禁用，使用内存存储');
          return {
            type: 'sqlite',
            database: ':memory:',
            entities: [],
            synchronize: false,
            logging: false,
          };
        }

        console.log('📊 正在配置数据库连接...');
        
        return {
          type: 'mysql',
          host: configService.get<string>('DB_HOST', 'localhost'),
          port: configService.get<number>('DB_PORT', 3306),
          username: configService.get<string>('DB_USERNAME', 'root'),
          password: configService.get<string>('DB_PASSWORD', ''),
          database: configService.get<string>('DB_DATABASE', 'security_service'),
          entities: [
            SecurityPolicy,
            SecurityEvent,
            ThreatIntelligence,
            SecurityAudit,
            DeviceTrust,
            UserSession,
            SecurityMetrics,
          ],
          synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
          logging: configService.get<boolean>('DB_LOGGING', false),
          timezone: '+08:00',
          charset: 'utf8mb4',
          extra: {
            connectionLimit: configService.get<number>('DB_CONNECTION_LIMIT', 10),
            acquireTimeout: configService.get<number>('DB_ACQUIRE_TIMEOUT', 60000),
            timeout: configService.get<number>('DB_TIMEOUT', 60000),
          },
          retryAttempts: 3,
          retryDelay: 3000,
          autoLoadEntities: true,
        };
      },
      inject: [ConfigService],
    }),
    
    // 特性模块 - 只在数据库启用时加载
    TypeOrmModule.forFeature([
      SecurityPolicy,
      SecurityEvent,
      ThreatIntelligence,
      SecurityAudit,
      DeviceTrust,
      UserSession,
      SecurityMetrics,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {
  constructor(private configService: ConfigService) {
    const dbEnabled = this.configService.get<string>('DB_ENABLED', 'true') === 'true';
    
    if (dbEnabled) {
      console.log('📊 数据库模块已加载');
      console.log(`📍 数据库主机: ${this.configService.get('DB_HOST')}`);
      console.log(`📍 数据库端口: ${this.configService.get('DB_PORT')}`);
      console.log(`📍 数据库名称: ${this.configService.get('DB_DATABASE')}`);
    } else {
      console.log('📊 数据库模块已加载（内存模式）');
    }
  }
}
