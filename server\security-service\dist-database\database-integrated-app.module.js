"use strict";
/**
 * 数据库集成应用模块
 * 集成数据库支持的完整安全服务
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseIntegratedAppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const terminus_1 = require("@nestjs/terminus");
// 数据库模块
const database_module_1 = require("./database/database.module");
const database_service_1 = require("./database/database.service");
const database_health_indicator_1 = require("./database/database-health.indicator");
// 安全模块
const security_policy_repository_1 = require("./security/repositories/security-policy.repository");
const security_event_repository_1 = require("./security/repositories/security-event.repository");
// 控制器
const simple_controller_1 = require("./simple-controller");
const enhanced_security_controller_1 = require("./enhanced-security.controller");
// 服务
const enhanced_security_service_1 = require("./enhanced-security.service");
// 健康检查
const health_controller_1 = require("./health/health.controller");
let DatabaseIntegratedAppModule = class DatabaseIntegratedAppModule {
    constructor() {
        console.log('🔐 数据库集成安全防护体系服务模块已加载');
        console.log('✅ 数据库支持已集成');
        console.log('✅ 数据持久化功能已启用');
        console.log('✅ 安全事件存储已配置');
        console.log('✅ 策略管理已集成');
    }
};
exports.DatabaseIntegratedAppModule = DatabaseIntegratedAppModule;
exports.DatabaseIntegratedAppModule = DatabaseIntegratedAppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            // 配置模块
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
                cache: true,
            }),
            // 数据库模块
            database_module_1.DatabaseModule,
            // JWT模块
            jwt_1.JwtModule.register({
                secret: 'security-service-jwt-secret-key-change-in-production',
                signOptions: {
                    expiresIn: '24h',
                },
            }),
            // Passport模块
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            // 健康检查模块
            terminus_1.TerminusModule,
        ],
        controllers: [
            simple_controller_1.SimpleController,
            enhanced_security_controller_1.EnhancedSecurityController,
            health_controller_1.HealthController,
        ],
        providers: [
            // 数据库服务
            database_service_1.DatabaseService,
            database_health_indicator_1.DatabaseHealthIndicator,
            // 仓库服务
            security_policy_repository_1.SecurityPolicyRepository,
            security_event_repository_1.SecurityEventRepository,
            // 业务服务
            enhanced_security_service_1.EnhancedSecurityService,
        ],
        exports: [
            database_service_1.DatabaseService,
            enhanced_security_service_1.EnhancedSecurityService,
            security_policy_repository_1.SecurityPolicyRepository,
            security_event_repository_1.SecurityEventRepository,
        ],
    }),
    __metadata("design:paramtypes", [])
], DatabaseIntegratedAppModule);
