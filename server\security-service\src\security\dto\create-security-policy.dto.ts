/**
 * 创建安全策略DTO
 */

import {
  IsString,
  IsEnum,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsArray,
  IsObject,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum PolicyTypeDto {
  ACCESS_CONTROL = 'access_control',
  DATA_PROTECTION = 'data_protection',
  NETWORK_SECURITY = 'network_security',
  DEVICE_COMPLIANCE = 'device_compliance',
  ZERO_TRUST = 'zero_trust',
}

export enum EnforcementModeDto {
  MONITOR = 'monitor',
  ENFORCE = 'enforce',
  BLOCK = 'block',
}

export class PolicyScopeDto {
  @ApiPropertyOptional({ type: [String], description: '用户范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  users?: string[];

  @ApiPropertyOptional({ type: [String], description: '设备范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  devices?: string[];

  @ApiPropertyOptional({ type: [String], description: '应用范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  applications?: string[];

  @ApiPropertyOptional({ type: [String], description: '网络范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  networks?: string[];

  @ApiPropertyOptional({ type: [String], description: '资源范围' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  resources?: string[];

  @ApiPropertyOptional({ type: [Object], description: '时间窗口' })
  @IsOptional()
  @IsArray()
  timeWindows?: any[];

  @ApiPropertyOptional({ type: [Object], description: '地理位置' })
  @IsOptional()
  @IsArray()
  locations?: any[];
}

export class SecurityRuleDto {
  @ApiProperty({ description: '规则ID' })
  @IsString()
  ruleId: string;

  @ApiProperty({ description: '规则类型' })
  @IsString()
  type: string;

  @ApiProperty({ description: '规则条件' })
  @IsObject()
  condition: any;

  @ApiProperty({ description: '规则动作' })
  @IsObject()
  action: any;

  @ApiPropertyOptional({ description: '规则权重', minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  weight?: number;
}

export class CreateSecurityPolicyDto {
  @ApiProperty({ description: '策略名称' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '策略描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    enum: PolicyTypeDto,
    description: '策略类型',
    default: PolicyTypeDto.ACCESS_CONTROL,
  })
  @IsEnum(PolicyTypeDto)
  policyType: PolicyTypeDto;

  @ApiPropertyOptional({
    enum: EnforcementModeDto,
    description: '执行模式',
    default: EnforcementModeDto.MONITOR,
  })
  @IsOptional()
  @IsEnum(EnforcementModeDto)
  enforcementMode?: EnforcementModeDto;

  @ApiPropertyOptional({
    description: '策略优先级',
    minimum: 1,
    maximum: 10,
    default: 5,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ type: PolicyScopeDto, description: '策略范围' })
  @IsOptional()
  @ValidateNested()
  @Type(() => PolicyScopeDto)
  scope?: PolicyScopeDto;

  @ApiPropertyOptional({ type: [SecurityRuleDto], description: '安全规则' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SecurityRuleDto)
  rules?: SecurityRuleDto[];

  @ApiPropertyOptional({ description: '策略条件' })
  @IsOptional()
  @IsObject()
  conditions?: any;

  @ApiPropertyOptional({ description: '策略动作' })
  @IsOptional()
  @IsObject()
  actions?: any;

  @ApiPropertyOptional({ description: '生效开始时间' })
  @IsOptional()
  effectiveFrom?: Date;

  @ApiPropertyOptional({ description: '生效结束时间' })
  @IsOptional()
  effectiveTo?: Date;

  @ApiPropertyOptional({ description: '是否启用', default: true })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiPropertyOptional({ description: '元数据' })
  @IsOptional()
  @IsObject()
  metadata?: any;
}
