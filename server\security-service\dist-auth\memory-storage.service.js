"use strict";
/**
 * 内存存储服务
 * 提供内存中的数据存储功能
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MemoryStorageService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryStorageService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let MemoryStorageService = MemoryStorageService_1 = class MemoryStorageService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MemoryStorageService_1.name);
        // 内存存储
        this.securityEvents = new Map();
        this.securityPolicies = new Map();
        this.userSessions = new Map();
        this.deviceTrust = new Map();
        this.threatIntelligence = new Map();
        this.securityAudits = new Map();
        this.securityMetrics = new Map();
    }
    async onModuleInit() {
        await this.initializeMemoryStorage();
    }
    /**
     * 初始化内存存储
     */
    async initializeMemoryStorage() {
        try {
            this.logger.log('初始化内存存储服务...');
            // 初始化默认数据
            await this.initializeDefaultData();
            this.logger.log('内存存储服务初始化完成');
        }
        catch (error) {
            this.logger.error('内存存储服务初始化失败', error);
            throw error;
        }
    }
    /**
     * 初始化默认数据
     */
    async initializeDefaultData() {
        // 初始化默认安全策略
        const defaultPolicies = [
            {
                id: 'policy_001',
                policyId: 'default_access_control',
                name: '默认访问控制策略',
                description: '系统默认的访问控制策略',
                policyType: 'access_control',
                enforcementMode: 'enforce',
                status: 'active',
                priority: 1,
                enabled: true,
                rules: [
                    {
                        ruleId: 'rule_001',
                        type: 'trust_score',
                        condition: { trustScore: { gte: 70 } },
                        action: { type: 'allow' },
                        weight: 10,
                    }
                ],
                createdAt: new Date(),
                updatedAt: new Date(),
            },
            {
                id: 'policy_002',
                policyId: 'zero_trust_default',
                name: '零信任默认策略',
                description: '零信任架构的默认安全策略',
                policyType: 'zero_trust',
                enforcementMode: 'monitor',
                status: 'active',
                priority: 2,
                enabled: true,
                rules: [
                    {
                        ruleId: 'rule_002',
                        type: 'device_compliance',
                        condition: { deviceCompliant: true },
                        action: { type: 'allow' },
                        weight: 8,
                    }
                ],
                createdAt: new Date(),
                updatedAt: new Date(),
            }
        ];
        defaultPolicies.forEach(policy => {
            this.securityPolicies.set(policy.id, policy);
        });
        // 初始化安全指标
        const initialMetrics = {
            id: 'metrics_001',
            metricType: 'general',
            totalEvents: 0,
            criticalEvents: 0,
            highEvents: 0,
            mediumEvents: 0,
            lowEvents: 0,
            resolvedEvents: 0,
            activeThreats: 0,
            blockedAttacks: 0,
            successfulAuthentications: 0,
            failedAuthentications: 0,
            activeSessions: 0,
            trustedDevices: 0,
            complianceScore: 100,
            riskScore: 0,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.securityMetrics.set(initialMetrics.id, initialMetrics);
        this.logger.log(`已初始化 ${defaultPolicies.length} 个默认安全策略`);
        this.logger.log('已初始化安全指标');
    }
    // ==================== 安全事件存储 ====================
    async createSecurityEvent(eventData) {
        const eventId = `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const event = {
            id: eventId,
            ...eventData,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.securityEvents.set(eventId, event);
        this.logger.log(`安全事件已创建: ${eventId}`);
        return event;
    }
    async getSecurityEvent(id) {
        return this.securityEvents.get(id) || null;
    }
    async getAllSecurityEvents() {
        return Array.from(this.securityEvents.values());
    }
    async getSecurityEventsBySeverity(severity) {
        return Array.from(this.securityEvents.values())
            .filter(event => event.severity === severity);
    }
    async updateSecurityEvent(id, updateData) {
        const event = this.securityEvents.get(id);
        if (event) {
            const updatedEvent = { ...event, ...updateData, updatedAt: new Date() };
            this.securityEvents.set(id, updatedEvent);
            return updatedEvent;
        }
        return null;
    }
    async deleteSecurityEvent(id) {
        return this.securityEvents.delete(id);
    }
    // ==================== 安全策略存储 ====================
    async createSecurityPolicy(policyData) {
        const policyId = `policy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const policy = {
            id: policyId,
            ...policyData,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.securityPolicies.set(policyId, policy);
        this.logger.log(`安全策略已创建: ${policyId}`);
        return policy;
    }
    async getSecurityPolicy(id) {
        return this.securityPolicies.get(id) || null;
    }
    async getAllSecurityPolicies() {
        return Array.from(this.securityPolicies.values());
    }
    async getActivePolicies() {
        return Array.from(this.securityPolicies.values())
            .filter(policy => policy.status === 'active' && policy.enabled);
    }
    async updateSecurityPolicy(id, updateData) {
        const policy = this.securityPolicies.get(id);
        if (policy) {
            const updatedPolicy = { ...policy, ...updateData, updatedAt: new Date() };
            this.securityPolicies.set(id, updatedPolicy);
            return updatedPolicy;
        }
        return null;
    }
    // ==================== 设备信任存储 ====================
    async createDeviceTrust(deviceData) {
        const deviceId = deviceData.deviceId || `device_${Date.now()}`;
        const device = {
            id: deviceId,
            ...deviceData,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.deviceTrust.set(deviceId, device);
        return device;
    }
    async getDeviceTrust(deviceId) {
        return this.deviceTrust.get(deviceId) || null;
    }
    async updateDeviceTrust(deviceId, updateData) {
        const device = this.deviceTrust.get(deviceId);
        if (device) {
            const updatedDevice = { ...device, ...updateData, updatedAt: new Date() };
            this.deviceTrust.set(deviceId, updatedDevice);
            return updatedDevice;
        }
        return null;
    }
    // ==================== 统计信息 ====================
    async getStorageStatistics() {
        return {
            securityEvents: this.securityEvents.size,
            securityPolicies: this.securityPolicies.size,
            userSessions: this.userSessions.size,
            deviceTrust: this.deviceTrust.size,
            threatIntelligence: this.threatIntelligence.size,
            securityAudits: this.securityAudits.size,
            securityMetrics: this.securityMetrics.size,
            totalRecords: this.securityEvents.size + this.securityPolicies.size +
                this.userSessions.size + this.deviceTrust.size +
                this.threatIntelligence.size + this.securityAudits.size +
                this.securityMetrics.size,
        };
    }
    /**
     * 获取健康状态
     */
    async getHealthStatus() {
        const stats = await this.getStorageStatistics();
        return {
            status: 'healthy',
            message: '内存存储正常运行',
            storage: 'memory',
            statistics: stats,
        };
    }
    /**
     * 清理旧数据
     */
    async cleanupOldData(daysToKeep = 30) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        let cleanedEvents = 0;
        let cleanedAudits = 0;
        // 清理旧的安全事件
        for (const [id, event] of this.securityEvents.entries()) {
            if (event.createdAt < cutoffDate) {
                this.securityEvents.delete(id);
                cleanedEvents++;
            }
        }
        // 清理旧的审计记录
        for (const [id, audit] of this.securityAudits.entries()) {
            if (audit.createdAt < cutoffDate) {
                this.securityAudits.delete(id);
                cleanedAudits++;
            }
        }
        this.logger.log(`数据清理完成: 清理了 ${cleanedEvents} 个事件, ${cleanedAudits} 个审计记录`);
        return {
            cleanedEvents,
            cleanedAudits,
            cutoffDate,
        };
    }
};
exports.MemoryStorageService = MemoryStorageService;
exports.MemoryStorageService = MemoryStorageService = MemoryStorageService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MemoryStorageService);
