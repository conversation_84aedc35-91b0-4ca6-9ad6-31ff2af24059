/**
 * 增强版应用模块
 * 集成核心安全功能
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

// 控制器
import { SimpleController } from './simple-controller';
import { EnhancedSecurityController } from './enhanced-security.controller';

// 服务
import { EnhancedSecurityService } from './enhanced-security.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // JWT模块
    JwtModule.register({
      secret: 'security-service-jwt-secret-key-change-in-production',
      signOptions: {
        expiresIn: '24h',
      },
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
  ],
  controllers: [
    SimpleController,
    EnhancedSecurityController,
  ],
  providers: [
    EnhancedSecurityService,
  ],
  exports: [
    EnhancedSecurityService,
  ],
})
export class EnhancedAppModule {
  constructor() {
    console.log('🔐 增强版安全防护体系服务模块已加载');
    console.log('✅ 核心安全功能已集成');
  }
}
