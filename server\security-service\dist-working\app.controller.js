"use strict";
/**
 * 安全防护体系服务应用控制器
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AppController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_service_1 = require("./app.service");
const jwt_auth_guard_1 = require("./auth/guards/jwt-auth.guard");
const public_decorator_1 = require("./common/decorators/public.decorator");
let AppController = AppController_1 = class AppController {
    constructor(appService) {
        this.appService = appService;
        this.logger = new common_1.Logger(AppController_1.name);
    }
    getServiceInfo() {
        this.logger.log('获取服务信息');
        return this.appService.getServiceInfo();
    }
    getVersion() {
        this.logger.log('获取服务版本');
        return this.appService.getVersion();
    }
    async getStatus() {
        this.logger.log('获取服务状态');
        return await this.appService.getStatus();
    }
    async getMetrics() {
        this.logger.log('获取服务指标');
        return await this.appService.getMetrics();
    }
    async performSecurityCheck(checkRequest) {
        this.logger.log('执行安全检查', checkRequest);
        return await this.appService.performSecurityCheck(checkRequest);
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务信息' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '服务信息',
        schema: {
            type: 'object',
            properties: {
                name: { type: 'string', example: '安全防护体系服务' },
                version: { type: 'string', example: '1.0.0' },
                description: { type: 'string', example: '零信任安全架构、区块链数据保护、AI安全防护、隐私计算' },
                status: { type: 'string', example: 'running' },
                timestamp: { type: 'string', format: 'date-time' },
                features: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['零信任安全', '区块链保护', 'AI安全防护', '隐私计算']
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getServiceInfo", null);
__decorate([
    (0, common_1.Get)('version'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务版本' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '服务版本信息',
        schema: {
            type: 'object',
            properties: {
                version: { type: 'string', example: '1.0.0' },
                buildTime: { type: 'string', format: 'date-time' },
                gitCommit: { type: 'string', example: 'abc123' },
                environment: { type: 'string', example: 'production' }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AppController.prototype, "getVersion", null);
__decorate([
    (0, common_1.Get)('status'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务状态' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '服务状态信息',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                uptime: { type: 'number', example: 3600 },
                memory: {
                    type: 'object',
                    properties: {
                        used: { type: 'number' },
                        total: { type: 'number' },
                        percentage: { type: 'number' }
                    }
                },
                cpu: {
                    type: 'object',
                    properties: {
                        usage: { type: 'number' },
                        loadAverage: { type: 'array', items: { type: 'number' } }
                    }
                },
                security: {
                    type: 'object',
                    properties: {
                        activePolicies: { type: 'number' },
                        activeSessions: { type: 'number' },
                        threatLevel: { type: 'string' },
                        lastSecurityCheck: { type: 'string', format: 'date-time' }
                    }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getStatus", null);
__decorate([
    (0, common_1.Get)('metrics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '获取服务指标' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '服务指标信息',
        schema: {
            type: 'object',
            properties: {
                requests: {
                    type: 'object',
                    properties: {
                        total: { type: 'number' },
                        success: { type: 'number' },
                        error: { type: 'number' },
                        rate: { type: 'number' }
                    }
                },
                security: {
                    type: 'object',
                    properties: {
                        authenticationAttempts: { type: 'number' },
                        successfulAuthentications: { type: 'number' },
                        failedAuthentications: { type: 'number' },
                        blockedRequests: { type: 'number' },
                        securityEvents: { type: 'number' }
                    }
                },
                performance: {
                    type: 'object',
                    properties: {
                        averageResponseTime: { type: 'number' },
                        p95ResponseTime: { type: 'number' },
                        p99ResponseTime: { type: 'number' }
                    }
                }
            }
        }
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "getMetrics", null);
__decorate([
    (0, common_1.Post)('security-check'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: '执行安全检查' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '安全检查结果',
        schema: {
            type: 'object',
            properties: {
                checkId: { type: 'string' },
                timestamp: { type: 'string', format: 'date-time' },
                status: { type: 'string', example: 'completed' },
                results: {
                    type: 'object',
                    properties: {
                        overallScore: { type: 'number' },
                        threatLevel: { type: 'string' },
                        vulnerabilities: { type: 'array', items: { type: 'object' } },
                        recommendations: { type: 'array', items: { type: 'string' } }
                    }
                }
            }
        }
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AppController.prototype, "performSecurityCheck", null);
exports.AppController = AppController = AppController_1 = __decorate([
    (0, swagger_1.ApiTags)('app'),
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [app_service_1.AppService])
], AppController);
