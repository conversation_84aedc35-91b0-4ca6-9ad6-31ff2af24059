/**
 * 安全策略仓库
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { SecurityPolicy, PolicyStatus, PolicyType } from '../entities/security-policy.entity';

@Injectable()
export class SecurityPolicyRepository {
  private readonly logger = new Logger(SecurityPolicyRepository.name);

  constructor(
    @InjectRepository(SecurityPolicy)
    private repository: Repository<SecurityPolicy>,
  ) {}

  /**
   * 创建安全策略
   */
  async create(policyData: Partial<SecurityPolicy>): Promise<SecurityPolicy> {
    try {
      const policy = this.repository.create(policyData);
      const savedPolicy = await this.repository.save(policy);
      this.logger.log(`安全策略已创建: ${savedPolicy.policyId}`);
      return savedPolicy;
    } catch (error) {
      this.logger.error('创建安全策略失败', error);
      throw error;
    }
  }

  /**
   * 根据ID查找策略
   */
  async findById(id: string): Promise<SecurityPolicy | null> {
    try {
      return await this.repository.findOne({ where: { id } });
    } catch (error) {
      this.logger.error('查找安全策略失败', error);
      throw error;
    }
  }

  /**
   * 根据策略ID查找策略
   */
  async findByPolicyId(policyId: string): Promise<SecurityPolicy | null> {
    try {
      return await this.repository.findOne({ where: { policyId } });
    } catch (error) {
      this.logger.error('查找安全策略失败', error);
      throw error;
    }
  }

  /**
   * 获取所有活跃策略
   */
  async findActivePolicies(): Promise<SecurityPolicy[]> {
    try {
      return await this.repository.find({
        where: {
          status: PolicyStatus.ACTIVE,
          enabled: true,
        },
        order: { priority: 'ASC' },
      });
    } catch (error) {
      this.logger.error('获取活跃策略失败', error);
      throw error;
    }
  }

  /**
   * 根据类型获取策略
   */
  async findByType(policyType: PolicyType): Promise<SecurityPolicy[]> {
    try {
      return await this.repository.find({
        where: {
          policyType,
          enabled: true,
        },
        order: { priority: 'ASC' },
      });
    } catch (error) {
      this.logger.error('根据类型获取策略失败', error);
      throw error;
    }
  }

  /**
   * 更新策略
   */
  async update(id: string, updateData: Partial<SecurityPolicy>): Promise<SecurityPolicy> {
    try {
      await this.repository.update(id, updateData);
      const updatedPolicy = await this.findById(id);
      if (updatedPolicy) {
        this.logger.log(`安全策略已更新: ${updatedPolicy.policyId}`);
      }
      return updatedPolicy;
    } catch (error) {
      this.logger.error('更新安全策略失败', error);
      throw error;
    }
  }

  /**
   * 删除策略
   */
  async delete(id: string): Promise<void> {
    try {
      const policy = await this.findById(id);
      if (policy) {
        await this.repository.delete(id);
        this.logger.log(`安全策略已删除: ${policy.policyId}`);
      }
    } catch (error) {
      this.logger.error('删除安全策略失败', error);
      throw error;
    }
  }

  /**
   * 获取策略统计
   */
  async getStatistics(): Promise<any> {
    try {
      const [total, active, inactive] = await Promise.all([
        this.repository.count(),
        this.repository.count({ where: { status: PolicyStatus.ACTIVE } }),
        this.repository.count({ where: { status: PolicyStatus.INACTIVE } }),
      ]);

      return {
        total,
        active,
        inactive,
        draft: total - active - inactive,
      };
    } catch (error) {
      this.logger.error('获取策略统计失败', error);
      throw error;
    }
  }

  /**
   * 分页查询策略
   */
  async findWithPagination(
    page: number = 1,
    limit: number = 10,
    filters?: any,
  ): Promise<{ policies: SecurityPolicy[]; total: number; totalPages: number }> {
    try {
      const skip = (page - 1) * limit;
      const where: FindOptionsWhere<SecurityPolicy> = {};

      // 应用过滤器
      if (filters?.status) {
        where.status = filters.status;
      }
      if (filters?.policyType) {
        where.policyType = filters.policyType;
      }
      if (filters?.enabled !== undefined) {
        where.enabled = filters.enabled;
      }

      const [policies, total] = await this.repository.findAndCount({
        where,
        skip,
        take: limit,
        order: { priority: 'ASC', createdAt: 'DESC' },
      });

      return {
        policies,
        total,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error('分页查询策略失败', error);
      throw error;
    }
  }

  /**
   * 批量更新策略状态
   */
  async batchUpdateStatus(ids: string[], status: PolicyStatus): Promise<void> {
    try {
      await this.repository.update(ids, { status });
      this.logger.log(`批量更新策略状态: ${ids.length} 个策略更新为 ${status}`);
    } catch (error) {
      this.logger.error('批量更新策略状态失败', error);
      throw error;
    }
  }

  /**
   * 搜索策略
   */
  async search(keyword: string): Promise<SecurityPolicy[]> {
    try {
      return await this.repository
        .createQueryBuilder('policy')
        .where('policy.name LIKE :keyword', { keyword: `%${keyword}%` })
        .orWhere('policy.description LIKE :keyword', { keyword: `%${keyword}%` })
        .orWhere('policy.policyId LIKE :keyword', { keyword: `%${keyword}%` })
        .orderBy('policy.priority', 'ASC')
        .getMany();
    } catch (error) {
      this.logger.error('搜索策略失败', error);
      throw error;
    }
  }
}
