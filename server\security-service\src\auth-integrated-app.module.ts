/**
 * 认证集成应用模块
 * 集成身份认证与授权系统的完整安全服务
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TerminusModule } from '@nestjs/terminus';

// 认证模块
import { AuthModule } from './auth/auth.module';

// 控制器
import { SimpleController } from './simple-controller';
import { EnhancedSecurityController } from './enhanced-security.controller';

// 服务
import { EnhancedSecurityService } from './enhanced-security.service';

// 健康检查
import { MemoryHealthController } from './memory-health.controller';

// 内存存储服务
import { MemoryStorageService } from './memory-storage.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
    }),

    // 认证模块
    AuthModule,

    // JWT模块（全局配置）
    JwtModule.register({
      secret: 'security-service-jwt-secret-key-change-in-production',
      signOptions: {
        expiresIn: '1h',
      },
    }),

    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // 健康检查模块
    TerminusModule,
  ],
  controllers: [
    SimpleController,
    EnhancedSecurityController,
    MemoryHealthController,
  ],
  providers: [
    // 内存存储服务
    MemoryStorageService,
    
    // 业务服务
    EnhancedSecurityService,
  ],
  exports: [
    AuthModule,
    MemoryStorageService,
    EnhancedSecurityService,
  ],
})
export class AuthIntegratedAppModule {
  constructor() {
    console.log('🔐 认证集成安全防护体系服务模块已加载');
    console.log('✅ 身份认证与授权系统已集成');
    console.log('✅ JWT令牌认证已启用');
    console.log('✅ 角色权限控制已配置');
    console.log('✅ 用户管理功能已启用');
    console.log('✅ 会话管理已集成');
    console.log('✅ 安全审计已启用');
    console.log('✅ 多因素认证支持已配置');
  }
}
