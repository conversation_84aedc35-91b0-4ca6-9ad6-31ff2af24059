/**
 * 安全指标实体
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('security_metrics')
@Index(['metricType'])
@Index(['createdAt'])
export class SecurityMetrics {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ default: 'general' })
  metricType: string;

  @Column({ type: 'int', default: 0 })
  totalEvents: number;

  @Column({ type: 'int', default: 0 })
  criticalEvents: number;

  @Column({ type: 'int', default: 0 })
  highEvents: number;

  @Column({ type: 'int', default: 0 })
  mediumEvents: number;

  @Column({ type: 'int', default: 0 })
  lowEvents: number;

  @Column({ type: 'int', default: 0 })
  resolvedEvents: number;

  @Column({ type: 'int', default: 0 })
  activeThreats: number;

  @Column({ type: 'int', default: 0 })
  blockedAttacks: number;

  @Column({ type: 'int', default: 0 })
  successfulAuthentications: number;

  @Column({ type: 'int', default: 0 })
  failedAuthentications: number;

  @Column({ type: 'int', default: 0 })
  activeSessions: number;

  @Column({ type: 'int', default: 0 })
  trustedDevices: number;

  @Column({ type: 'int', default: 100 })
  complianceScore: number;

  @Column({ type: 'int', default: 0 })
  riskScore: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
